package inventory

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetInventoryItemDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetInventoryItemDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetInventoryItemDetailLogic {
	return &GetInventoryItemDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *GetInventoryItemDetailLogic) GetInventoryItemDetail(req *types.GetInventoryItemDetailReq) (resp *types.GetInventoryItemDetailResp, err error) {
	// todo: add your logic here and delete this line

	return
}
