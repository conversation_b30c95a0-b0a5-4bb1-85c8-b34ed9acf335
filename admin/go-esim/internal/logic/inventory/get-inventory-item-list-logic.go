package inventory

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetInventoryItemListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetInventoryItemListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetInventoryItemListLogic {
	return &GetInventoryItemListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *GetInventoryItemListLogic) GetInventoryItemList(req *types.GetInventoryItemListReq) (resp *types.GetInventoryItemListResp, err error) {
	// todo: add your logic here and delete this line

	return
}
