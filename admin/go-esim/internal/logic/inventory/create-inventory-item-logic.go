package inventory

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateInventoryItemLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateInventoryItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateInventoryItemLogic {
	return &CreateInventoryItemLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *CreateInventoryItemLogic) CreateInventoryItem(req *types.CreateInventoryItemReq) (resp *types.CreateInventoryItemResp, err error) {
	// todo: add your logic here and delete this line

	return
}
