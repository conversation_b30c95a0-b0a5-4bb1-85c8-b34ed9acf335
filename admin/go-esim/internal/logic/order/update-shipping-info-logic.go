package order

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateShippingInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateShippingInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateShippingInfoLogic {
	return &UpdateShippingInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *UpdateShippingInfoLogic) UpdateShippingInfo(req *types.UpdateShippingInfoReq) (resp *types.UpdateShippingInfoResp, err error) {
	// todo: add your logic here and delete this line

	return
}
