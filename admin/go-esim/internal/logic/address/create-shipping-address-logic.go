package address

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateShippingAddressLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateShippingAddressLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateShippingAddressLogic {
	return &CreateShippingAddressLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *CreateShippingAddressLogic) CreateShippingAddress(req *types.CreateShippingAddressReq) (resp *types.CreateShippingAddressResp, err error) {
	// todo: add your logic here and delete this line

	return
}
