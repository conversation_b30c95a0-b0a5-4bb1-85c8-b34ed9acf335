package address

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetShippingAddressListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetShippingAddressListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetShippingAddressListLogic {
	return &GetShippingAddressListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *GetShippingAddressListLogic) GetShippingAddressList(req *types.GetShippingAddressListReq) (resp *types.GetShippingAddressListResp, err error) {
	// todo: add your logic here and delete this line

	return
}
