package address

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetShippingAddressDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetShippingAddressDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetShippingAddressDetailLogic {
	return &GetShippingAddressDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *GetShippingAddressDetailLogic) GetShippingAddressDetail(req *types.GetShippingAddressDetailReq) (resp *types.GetShippingAddressDetailResp, err error) {
	// todo: add your logic here and delete this line

	return
}
