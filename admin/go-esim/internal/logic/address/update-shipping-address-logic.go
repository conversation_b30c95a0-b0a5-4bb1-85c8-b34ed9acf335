package address

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateShippingAddressLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateShippingAddressLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateShippingAddressLogic {
	return &UpdateShippingAddressLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *UpdateShippingAddressLogic) UpdateShippingAddress(req *types.UpdateShippingAddressReq) (resp *types.UpdateShippingAddressResp, err error) {
	// todo: add your logic here and delete this line

	return
}
