package product

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type BatchUpdateProductStatusLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBatchUpdateProductStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BatchUpdateProductStatusLogic {
	return &BatchUpdateProductStatusLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *BatchUpdateProductStatusLogic) BatchUpdateProductStatus(req *types.BatchUpdateProductStatusReq) (resp *types.BatchUpdateProductStatusResp, err error) {
	// todo: add your logic here and delete this line

	return
}
