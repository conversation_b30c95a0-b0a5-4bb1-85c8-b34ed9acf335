package inventory

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/inventory"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route post /warehouse/inventory/item/create inventory CreateInventoryItem
//

//

//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: CreateInventoryItemReq
//
// Responses:
//  200: CreateInventoryItemResp

func CreateInventoryItemHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.CreateInventoryItemReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := inventory.NewCreateInventoryItemLogic(r.Context(), svcCtx)
		resp, err := l.CreateInventoryItem(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
