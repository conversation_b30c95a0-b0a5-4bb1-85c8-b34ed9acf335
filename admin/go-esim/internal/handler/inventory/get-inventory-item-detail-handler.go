package inventory

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/inventory"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route get /warehouse/inventory/item/detail/{id} inventory GetInventoryItemDetail
//

//

//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: GetInventoryItemDetailReq
//
// Responses:
//  200: GetInventoryItemDetailResp

func GetInventoryItemDetailHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetInventoryItemDetailReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := inventory.NewGetInventoryItemDetailLogic(r.Context(), svcCtx)
		resp, err := l.GetInventoryItemDetail(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
