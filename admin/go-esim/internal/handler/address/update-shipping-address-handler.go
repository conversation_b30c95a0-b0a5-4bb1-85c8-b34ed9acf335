package address

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/address"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route put /mall/address/update address UpdateShippingAddress
//

//

//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: UpdateShippingAddressReq
//
// Responses:
//  200: UpdateShippingAddressResp

func UpdateShippingAddressHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UpdateShippingAddressReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := address.NewUpdateShippingAddressLogic(r.Context(), svcCtx)
		resp, err := l.UpdateShippingAddress(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
