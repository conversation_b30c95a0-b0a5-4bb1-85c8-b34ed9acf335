package address

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/address"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route delete /mall/address/delete address DeleteShippingAddress
//

//

//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: DeleteShippingAddressReq
//
// Responses:
//  200: DeleteShippingAddressResp

func DeleteShippingAddressHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.DeleteShippingAddressReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := address.NewDeleteShippingAddressLogic(r.Context(), svcCtx)
		resp, err := l.DeleteShippingAddress(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
