package address

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/address"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route put /mall/address/default address SetDefaultAddress
//

//

//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: SetDefaultAddressReq
//
// Responses:
//  200: SetDefaultAddressResp

func SetDefaultAddressHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.SetDefaultAddressReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := address.NewSetDefaultAddressLogic(r.Context(), svcCtx)
		resp, err := l.Set<PERSON>efault<PERSON>ddress(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
