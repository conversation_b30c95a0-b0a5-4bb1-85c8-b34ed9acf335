package address

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/address"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route get /mall/address/list address GetShippingAddressList
//

//

//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: GetShippingAddressListReq
//
// Responses:
//  200: GetShippingAddressListResp

func GetShippingAddressListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetShippingAddressListReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := address.NewGetShippingAddressListLogic(r.Context(), svcCtx)
		resp, err := l.GetShippingAddressList(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
