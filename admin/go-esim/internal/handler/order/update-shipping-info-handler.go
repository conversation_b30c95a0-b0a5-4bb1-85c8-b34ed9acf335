package order

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/order"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route put /mall/order/shipping order UpdateShippingInfo
//

//

//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: UpdateShippingInfoReq
//
// Responses:
//  200: UpdateShippingInfoResp

func UpdateShippingInfoHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UpdateShippingInfoReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := order.NewUpdateShippingInfoLogic(r.Context(), svcCtx)
		resp, err := l.UpdateShippingInfo(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
