package product

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/product"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route put /mall/product/batch/status product BatchUpdateProductStatus
//

//

//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: BatchUpdateProductStatusReq
//
// Responses:
//  200: BatchUpdateProductStatusResp

func BatchUpdateProductStatusHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.BatchUpdateProductStatusReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := product.NewBatchUpdateProductStatusLogic(r.Context(), svcCtx)
		resp, err := l.BatchUpdateProductStatus(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
