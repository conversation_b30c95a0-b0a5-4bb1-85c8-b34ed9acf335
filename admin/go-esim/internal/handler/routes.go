// Code generated by goctl. DO NOT EDIT.
// goctls v1.10.12

package handler

import (
	"net/http"

	address "github.com/Wenpiner/iot-api/internal/handler/address"
	card "github.com/Wenpiner/iot-api/internal/handler/card"
	category "github.com/Wenpiner/iot-api/internal/handler/category"
	delivery "github.com/Wenpiner/iot-api/internal/handler/delivery"
	device "github.com/Wenpiner/iot-api/internal/handler/device"
	entry "github.com/Wenpiner/iot-api/internal/handler/entry"
	inventory "github.com/Wenpiner/iot-api/internal/handler/inventory"
	order "github.com/Wenpiner/iot-api/internal/handler/order"
	product "github.com/Wenpiner/iot-api/internal/handler/product"
	qimen "github.com/Wenpiner/iot-api/internal/handler/qimen"
	task "github.com/Wenpiner/iot-api/internal/handler/task"
	warehouse "github.com/Wenpiner/iot-api/internal/handler/warehouse"
	"github.com/Wenpiner/iot-api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/list",
					Handler: card.GetCardListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/import",
					Handler: card.ImportCardHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/channel/create",
					Handler: card.CreateChannelHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/channel/list",
					Handler: card.GetChannelListHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/card"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/list",
					Handler: task.GetTaskListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/log",
					Handler: task.GetTaskLogHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/esim-task"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/import",
					Handler: device.ImportDeviceHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/manufacturer/create",
					Handler: device.CreateManufacturerHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/manufacturer/list",
					Handler: device.GetManufacturerListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/list",
					Handler: device.GetDeviceListHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/device"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 获取云仓列表
				Method:  http.MethodGet,
				Path:    "/list",
				Handler: warehouse.GetWarehouseListHandler(serverCtx),
			},
			{
				// 获取云仓详情
				Method:  http.MethodGet,
				Path:    "/detail/:id",
				Handler: warehouse.GetWarehouseDetailHandler(serverCtx),
			},
			{
				// 创建云仓
				Method:  http.MethodPost,
				Path:    "/create",
				Handler: warehouse.CreateWarehouseHandler(serverCtx),
			},
			{
				// 更新云仓
				Method:  http.MethodPut,
				Path:    "/update",
				Handler: warehouse.UpdateWarehouseHandler(serverCtx),
			},
		},
		rest.WithPrefix("/warehouse"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 创建入库单
				Method:  http.MethodPost,
				Path:    "/order/create",
				Handler: entry.CreateEntryOrderHandler(serverCtx),
			},
			{
				// 获取入库单列表
				Method:  http.MethodGet,
				Path:    "/order/list",
				Handler: entry.GetEntryOrderListHandler(serverCtx),
			},
			{
				// 获取入库单详情
				Method:  http.MethodGet,
				Path:    "/order/detail/:id",
				Handler: entry.GetEntryOrderDetailHandler(serverCtx),
			},
			{
				// 确认入库
				Method:  http.MethodPut,
				Path:    "/order/confirm",
				Handler: entry.ConfirmEntryOrderHandler(serverCtx),
			},
			{
				// 取消入库单
				Method:  http.MethodPut,
				Path:    "/order/cancel",
				Handler: entry.CancelEntryOrderHandler(serverCtx),
			},
			{
				// 批量录入入库明细
				Method:  http.MethodPost,
				Path:    "/lines/batch",
				Handler: entry.BatchCreateEntryLinesHandler(serverCtx),
			},
		},
		rest.WithPrefix("/warehouse/entry"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 创建出库单
				Method:  http.MethodPost,
				Path:    "/order/create",
				Handler: delivery.CreateDeliveryOrderHandler(serverCtx),
			},
			{
				// 获取出库单列表
				Method:  http.MethodGet,
				Path:    "/order/list",
				Handler: delivery.GetDeliveryOrderListHandler(serverCtx),
			},
			{
				// 获取出库单详情
				Method:  http.MethodGet,
				Path:    "/order/detail/:id",
				Handler: delivery.GetDeliveryOrderDetailHandler(serverCtx),
			},
			{
				// 确认出库
				Method:  http.MethodPut,
				Path:    "/order/confirm",
				Handler: delivery.ConfirmDeliveryOrderHandler(serverCtx),
			},
			{
				// 发货确认
				Method:  http.MethodPut,
				Path:    "/order/ship",
				Handler: delivery.ShipDeliveryOrderHandler(serverCtx),
			},
			{
				// 收货确认
				Method:  http.MethodPut,
				Path:    "/order/receive",
				Handler: delivery.ReceiveDeliveryOrderHandler(serverCtx),
			},
		},
		rest.WithPrefix("/warehouse/delivery"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 新增库存商品
				Method:  http.MethodPost,
				Path:    "/item/create",
				Handler: inventory.CreateInventoryItemHandler(serverCtx),
			},
			{
				// 获取库存商品列表
				Method:  http.MethodGet,
				Path:    "/item/list",
				Handler: inventory.GetInventoryItemListHandler(serverCtx),
			},
			{
				// 获取库存商品详情
				Method:  http.MethodGet,
				Path:    "/item/detail/:id",
				Handler: inventory.GetInventoryItemDetailHandler(serverCtx),
			},
			{
				// 查询库存
				Method:  http.MethodGet,
				Path:    "/query",
				Handler: inventory.QueryInventoryHandler(serverCtx),
			},
			{
				// 库存盘点
				Method:  http.MethodPost,
				Path:    "/check",
				Handler: inventory.InventoryCheckHandler(serverCtx),
			},
			{
				// 库存调整
				Method:  http.MethodPost,
				Path:    "/adjust",
				Handler: inventory.AdjustInventoryHandler(serverCtx),
			},
			{
				// 获取库存变动日志
				Method:  http.MethodGet,
				Path:    "/logs",
				Handler: inventory.GetInventoryLogsHandler(serverCtx),
			},
		},
		rest.WithPrefix("/warehouse/inventory"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 手动同步入库单到奇门
				Method:  http.MethodPost,
				Path:    "/sync/entry",
				Handler: qimen.SyncEntryOrderToQimenHandler(serverCtx),
			},
			{
				// 手动同步出库单到奇门
				Method:  http.MethodPost,
				Path:    "/sync/delivery",
				Handler: qimen.SyncDeliveryOrderToQimenHandler(serverCtx),
			},
			{
				// 从奇门同步库存
				Method:  http.MethodPost,
				Path:    "/sync/inventory",
				Handler: qimen.SyncInventoryFromQimenHandler(serverCtx),
			},
			{
				// 获取API同步日志
				Method:  http.MethodGet,
				Path:    "/sync/logs",
				Handler: qimen.GetQimenSyncLogsHandler(serverCtx),
			},
			{
				// 重试失败的同步任务
				Method:  http.MethodPost,
				Path:    "/sync/retry",
				Handler: qimen.RetryQimenSyncHandler(serverCtx),
			},
		},
		rest.WithPrefix("/warehouse/qimen"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 创建商品
				Method:  http.MethodPost,
				Path:    "/create",
				Handler: product.CreateProductHandler(serverCtx),
			},
			{
				// 更新商品
				Method:  http.MethodPut,
				Path:    "/update",
				Handler: product.UpdateProductHandler(serverCtx),
			},
			{
				// 获取商品列表
				Method:  http.MethodGet,
				Path:    "/list",
				Handler: product.GetProductListHandler(serverCtx),
			},
			{
				// 获取商品详情
				Method:  http.MethodGet,
				Path:    "/detail/:id",
				Handler: product.GetProductDetailHandler(serverCtx),
			},
			{
				// 删除商品
				Method:  http.MethodDelete,
				Path:    "/delete",
				Handler: product.DeleteProductHandler(serverCtx),
			},
			{
				// 批量更新商品状态
				Method:  http.MethodPut,
				Path:    "/batch/status",
				Handler: product.BatchUpdateProductStatusHandler(serverCtx),
			},
		},
		rest.WithPrefix("/mall/product"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 创建分类
				Method:  http.MethodPost,
				Path:    "/create",
				Handler: category.CreateCategoryHandler(serverCtx),
			},
			{
				// 更新分类
				Method:  http.MethodPut,
				Path:    "/update",
				Handler: category.UpdateCategoryHandler(serverCtx),
			},
			{
				// 获取分类列表
				Method:  http.MethodGet,
				Path:    "/list",
				Handler: category.GetCategoryListHandler(serverCtx),
			},
			{
				// 获取分类树
				Method:  http.MethodGet,
				Path:    "/tree",
				Handler: category.GetCategoryTreeHandler(serverCtx),
			},
			{
				// 获取分类详情
				Method:  http.MethodGet,
				Path:    "/detail/:id",
				Handler: category.GetCategoryDetailHandler(serverCtx),
			},
			{
				// 删除分类
				Method:  http.MethodDelete,
				Path:    "/delete",
				Handler: category.DeleteCategoryHandler(serverCtx),
			},
			{
				// 移动分类
				Method:  http.MethodPut,
				Path:    "/move",
				Handler: category.MoveCategoryHandler(serverCtx),
			},
		},
		rest.WithPrefix("/mall/category"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 获取订单列表
				Method:  http.MethodGet,
				Path:    "/list",
				Handler: order.GetOrderListHandler(serverCtx),
			},
			{
				// 获取订单详情
				Method:  http.MethodGet,
				Path:    "/detail/:id",
				Handler: order.GetOrderDetailHandler(serverCtx),
			},
			{
				// 更新订单状态
				Method:  http.MethodPut,
				Path:    "/status",
				Handler: order.UpdateOrderStatusHandler(serverCtx),
			},
			{
				// 更新发货信息
				Method:  http.MethodPut,
				Path:    "/shipping",
				Handler: order.UpdateShippingInfoHandler(serverCtx),
			},
			{
				// 取消订单
				Method:  http.MethodPut,
				Path:    "/cancel",
				Handler: order.CancelOrderHandler(serverCtx),
			},
		},
		rest.WithPrefix("/mall/order"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 创建收货地址
				Method:  http.MethodPost,
				Path:    "/create",
				Handler: address.CreateShippingAddressHandler(serverCtx),
			},
			{
				// 更新收货地址
				Method:  http.MethodPut,
				Path:    "/update",
				Handler: address.UpdateShippingAddressHandler(serverCtx),
			},
			{
				// 获取收货地址列表
				Method:  http.MethodGet,
				Path:    "/list",
				Handler: address.GetShippingAddressListHandler(serverCtx),
			},
			{
				// 获取收货地址详情
				Method:  http.MethodGet,
				Path:    "/detail/:id",
				Handler: address.GetShippingAddressDetailHandler(serverCtx),
			},
			{
				// 删除收货地址
				Method:  http.MethodDelete,
				Path:    "/delete",
				Handler: address.DeleteShippingAddressHandler(serverCtx),
			},
			{
				// 设置默认地址
				Method:  http.MethodPut,
				Path:    "/default",
				Handler: address.SetDefaultAddressHandler(serverCtx),
			},
		},
		rest.WithPrefix("/mall/address"),
	)
}
