create table iot.mall_products
(
    id                 bigint      default nextval('iot.products_id_seq'::regclass) not null
        constraint products_pkey
            primary key,
    product_code       varchar(100)                                                 not null
        constraint products_product_code_key
            unique,
    product_name       varchar(200)                                                 not null,
    product_title      varchar(500),
    short_description  varchar(1000),
    description        text,
    category_id        bigint,
    category_path      varchar(500),
    brand_id           bigint,
    brand_name         varchar(100),
    product_type       varchar(20) default 'PHYSICAL'::character varying,
    model              varchar(100),
    weight             numeric(8, 3),
    dimensions         varchar(100),
    market_price       numeric(10, 2),
    sale_price         numeric(10, 2),
    cost_price         numeric(10, 2),
    has_sku            boolean     default false,
    min_order_quantity integer     default 1,
    max_order_quantity integer,
    is_gift            boolean     default false,
    status             varchar(20) default 'DRAFT'::character varying,
    is_published       boolean     default false,
    is_featured        boolean     default false,
    sort_order         integer     default 0,
    main_image         varchar(500),
    launch_time        timestamp,
    created_at         timestamp   default CURRENT_TIMESTAMP,
    updated_at         timestamp   default CURRENT_TIMESTAMP,
    remark             text
);

comment on table iot.mall_products is '商品主表';

comment on column iot.mall_products.id is '主键ID，自增';

comment on column iot.mall_products.product_code is '商品编码，唯一';

comment on column iot.mall_products.product_name is '商品名称';

comment on column iot.mall_products.product_title is '商品标题（用于展示）';

comment on column iot.mall_products.short_description is '商品简介';

comment on column iot.mall_products.description is '商品详细描述';

comment on column iot.mall_products.category_id is '商品分类ID';

comment on column iot.mall_products.category_path is '分类路径（如：/数码/手机/智能手机）';

comment on column iot.mall_products.brand_id is '品牌ID';

comment on column iot.mall_products.brand_name is '品牌名称';

comment on column iot.mall_products.product_type is '商品类型（PHYSICAL-实物，VIRTUAL-虚拟，SERVICE-服务）';

comment on column iot.mall_products.model is '型号';

comment on column iot.mall_products.weight is '重量（千克）';

comment on column iot.mall_products.dimensions is '尺寸（长x宽x高）';

comment on column iot.mall_products.market_price is '市场价';

comment on column iot.mall_products.sale_price is '销售价';

comment on column iot.mall_products.cost_price is '成本价';

comment on column iot.mall_products.has_sku is '是否有SKU（多规格）';

comment on column iot.mall_products.min_order_quantity is '最小起订量';

comment on column iot.mall_products.max_order_quantity is '最大限购量';

comment on column iot.mall_products.is_gift is '是否为赠品';

comment on column iot.mall_products.status is '商品状态（DRAFT-草稿，ACTIVE-上架，INACTIVE-下架，DELETED-删除）';

comment on column iot.mall_products.is_published is '是否发布';

comment on column iot.mall_products.is_featured is '是否推荐';

comment on column iot.mall_products.sort_order is '排序权重';

comment on column iot.mall_products.main_image is '主图URL';

comment on column iot.mall_products.launch_time is '上架时间';

comment on column iot.mall_products.created_at is '创建时间';

comment on column iot.mall_products.updated_at is '更新时间';

comment on column iot.mall_products.remark is '备注信息';

alter table iot.mall_products
    owner to postgres;

create index idx_products_product_code
    on iot.mall_products (product_code);

create index idx_products_category_id
    on iot.mall_products (category_id);

create index idx_products_brand_id
    on iot.mall_products (brand_id);

create index idx_products_status
    on iot.mall_products (status);

create index idx_products_is_published
    on iot.mall_products (is_published);

create index idx_products_sort_order
    on iot.mall_products (sort_order);

create table iot.mall_product_categories
(
    id             bigserial
        primary key,
    category_code  varchar(100) not null
        unique,
    category_name  varchar(200) not null,
    category_alias varchar(200),
    description    text,
    parent_id      bigint      default 0
        constraint fk_mall_product_categories_parent_id
            references iot.mall_product_categories
            deferrable initially deferred,
    level          integer     default 1,
    path           varchar(1000),
    path_name      varchar(1000),
    icon           varchar(500),
    image          varchar(500),
    sort_order     integer     default 0,
    status         varchar(20) default 'ACTIVE'::character varying,
    is_leaf        boolean     default true,
    is_visible     boolean     default true,
    product_count  integer     default 0,
    children_count integer     default 0,
    created_at     timestamp   default CURRENT_TIMESTAMP,
    updated_at     timestamp   default CURRENT_TIMESTAMP,
    remark         text
);

comment on table iot.mall_product_categories is '商品分类表';

comment on column iot.mall_product_categories.id is '主键ID，自增';

comment on column iot.mall_product_categories.category_code is '分类编码，唯一';

comment on column iot.mall_product_categories.category_name is '分类名称';

comment on column iot.mall_product_categories.category_alias is '分类别名/显示名称';

comment on column iot.mall_product_categories.description is '分类描述';

comment on column iot.mall_product_categories.parent_id is '父分类ID（顶级分类为0）';

comment on column iot.mall_product_categories.level is '分类级别（1-一级分类，2-二级分类，以此类推）';

comment on column iot.mall_product_categories.path is '分类路径（如：1,3,15）';

comment on column iot.mall_product_categories.path_name is '分类名称路径（如：数码/手机/智能手机）';

comment on column iot.mall_product_categories.icon is '分类图标URL';

comment on column iot.mall_product_categories.image is '分类图片URL';

comment on column iot.mall_product_categories.sort_order is '排序权重';

comment on column iot.mall_product_categories.status is '分类状态（ACTIVE-启用，INACTIVE-禁用，DELETED-删除）';

comment on column iot.mall_product_categories.is_leaf is '是否为叶子节点（最终分类）';

comment on column iot.mall_product_categories.is_visible is '是否前台显示';

comment on column iot.mall_product_categories.product_count is '商品数量（可定期更新）';

comment on column iot.mall_product_categories.children_count is '子分类数量';

comment on column iot.mall_product_categories.created_at is '创建时间';

comment on column iot.mall_product_categories.updated_at is '更新时间';

comment on column iot.mall_product_categories.remark is '备注信息';

alter table iot.mall_product_categories
    owner to postgres;

create index idx_mall_product_categories_code
    on iot.mall_product_categories (category_code);

create index idx_mall_product_categories_parent_id
    on iot.mall_product_categories (parent_id);

create index idx_mall_product_categories_level
    on iot.mall_product_categories (level);

create index idx_mall_product_categories_status
    on iot.mall_product_categories (status);

create index idx_mall_product_categories_sort_order
    on iot.mall_product_categories (sort_order);

create index idx_mall_product_categories_path
    on iot.mall_product_categories (path);

create table iot.mall_shipping_addresses
(
    id              bigserial
        primary key,
    user_id         bigint       not null,
    consignee_name  varchar(100) not null,
    consignee_phone varchar(20)  not null,
    consignee_tel   varchar(20),
    province_id     integer,
    province_name   varchar(50),
    city_id         integer,
    city_name       varchar(50),
    district_id     integer,
    district_name   varchar(50),
    address_detail  varchar(500) not null,
    postal_code     varchar(10),
    longitude       numeric(10, 7),
    latitude        numeric(10, 7),
    address_label   varchar(20) default 'OTHER'::character varying,
    is_default      boolean     default false,
    is_active       boolean     default true,
    created_at      timestamp   default CURRENT_TIMESTAMP,
    updated_at      timestamp   default CURRENT_TIMESTAMP,
    last_used_at    timestamp,
    remark          text
);

comment on table iot.mall_shipping_addresses is '收货地址表';

comment on column iot.mall_shipping_addresses.id is '主键ID，自增';

comment on column iot.mall_shipping_addresses.user_id is '用户ID，关联用户表';

comment on column iot.mall_shipping_addresses.consignee_name is '收货人姓名';

comment on column iot.mall_shipping_addresses.consignee_phone is '收货人手机号';

comment on column iot.mall_shipping_addresses.consignee_tel is '收货人固定电话（可选）';

comment on column iot.mall_shipping_addresses.province_id is '省份ID';

comment on column iot.mall_shipping_addresses.province_name is '省份名称';

comment on column iot.mall_shipping_addresses.city_id is '城市ID';

comment on column iot.mall_shipping_addresses.city_name is '城市名称';

comment on column iot.mall_shipping_addresses.district_id is '区县ID';

comment on column iot.mall_shipping_addresses.district_name is '区县名称';

comment on column iot.mall_shipping_addresses.address_detail is '详细地址';

comment on column iot.mall_shipping_addresses.postal_code is '邮政编码';

comment on column iot.mall_shipping_addresses.longitude is '经度';

comment on column iot.mall_shipping_addresses.latitude is '纬度';

comment on column iot.mall_shipping_addresses.address_label is '地址标签（HOME-家，COMPANY-公司，OTHER-其他）';

comment on column iot.mall_shipping_addresses.is_default is '是否默认地址';

comment on column iot.mall_shipping_addresses.is_active is '是否有效';

comment on column iot.mall_shipping_addresses.created_at is '创建时间';

comment on column iot.mall_shipping_addresses.updated_at is '更新时间';

comment on column iot.mall_shipping_addresses.last_used_at is '最后使用时间';

comment on column iot.mall_shipping_addresses.remark is '备注信息';

alter table iot.mall_shipping_addresses
    owner to postgres;

create index idx_mall_shipping_addresses_user_id
    on iot.mall_shipping_addresses (user_id);

create index idx_mall_shipping_addresses_default
    on iot.mall_shipping_addresses (user_id, is_default);

create index idx_mall_shipping_addresses_active
    on iot.mall_shipping_addresses (is_active);

create index idx_mall_shipping_addresses_phone
    on iot.mall_shipping_addresses (consignee_phone);

create unique index idx_mall_shipping_addresses_user_default
    on iot.mall_shipping_addresses (user_id)
    where ((is_default = true) AND (is_active = true));

create table iot.mall_orders
(
    id                bigserial
        primary key,
    order_no          varchar(32)    not null
        unique,
    user_id           bigint         not null,
    order_type        varchar(20)    default 'NORMAL'::character varying,
    total_amount      numeric(12, 2) not null,
    product_amount    numeric(12, 2) not null,
    freight_amount    numeric(10, 2) default 0,
    discount_amount   numeric(10, 2) default 0,
    coupon_amount     numeric(10, 2) default 0,
    actual_amount     numeric(12, 2) not null,
    consignee_name    varchar(100)   not null,
    consignee_phone   varchar(20)    not null,
    shipping_province varchar(50),
    shipping_city     varchar(50),
    shipping_district varchar(50),
    shipping_address  varchar(500)   not null,
    order_status      varchar(20)    default 'PENDING'::character varying,
    payment_status    varchar(20)    default 'UNPAID'::character varying,
    shipping_status   varchar(20)    default 'UNSHIPPED'::character varying,
    payment_method    varchar(20),
    payment_no        varchar(64),
    paid_at           timestamp,
    logistics_company varchar(100),
    tracking_no       varchar(100),
    shipped_at        timestamp,
    delivered_at      timestamp,
    order_time        timestamp      default CURRENT_TIMESTAMP,
    expire_time       timestamp,
    created_at        timestamp      default CURRENT_TIMESTAMP,
    updated_at        timestamp      default CURRENT_TIMESTAMP,
    buyer_message     text,
    seller_message    text,
    remark            text
);

comment on table iot.mall_orders is '订单主表';

comment on column iot.mall_orders.id is '主键ID，自增';

comment on column iot.mall_orders.order_no is '订单号，唯一';

comment on column iot.mall_orders.user_id is '用户ID';

comment on column iot.mall_orders.order_type is '订单类型（NORMAL-普通订单，PRESALE-预售，GROUP-团购等）';

comment on column iot.mall_orders.total_amount is '订单总金额';

comment on column iot.mall_orders.product_amount is '商品总金额';

comment on column iot.mall_orders.freight_amount is '运费金额';

comment on column iot.mall_orders.discount_amount is '优惠金额';

comment on column iot.mall_orders.coupon_amount is '优惠券金额';

comment on column iot.mall_orders.actual_amount is '实付金额';

comment on column iot.mall_orders.consignee_name is '收货人姓名';

comment on column iot.mall_orders.consignee_phone is '收货人手机号';

comment on column iot.mall_orders.shipping_province is '收货省份';

comment on column iot.mall_orders.shipping_city is '收货城市';

comment on column iot.mall_orders.shipping_district is '收货区县';

comment on column iot.mall_orders.shipping_address is '收货详细地址';

comment on column iot.mall_orders.order_status is '订单状态（PENDING-待付款，PAID-已付款，SHIPPED-已发货，DELIVERED-已送达，COMPLETED-已完成，CANCELLED-已取消）';

comment on column iot.mall_orders.payment_status is '支付状态（UNPAID-未支付，PAID-已支付，REFUNDED-已退款）';

comment on column iot.mall_orders.shipping_status is '发货状态（UNSHIPPED-未发货，SHIPPED-已发货，DELIVERED-已送达）';

comment on column iot.mall_orders.payment_method is '支付方式（ALIPAY-支付宝，WECHAT-微信，BANK-银行卡等）';

comment on column iot.mall_orders.payment_no is '支付流水号';

comment on column iot.mall_orders.paid_at is '支付时间';

comment on column iot.mall_orders.logistics_company is '物流公司';

comment on column iot.mall_orders.tracking_no is '物流单号';

comment on column iot.mall_orders.shipped_at is '发货时间';

comment on column iot.mall_orders.delivered_at is '签收时间';

comment on column iot.mall_orders.order_time is '下单时间';

comment on column iot.mall_orders.expire_time is '订单过期时间（未支付自动取消）';

comment on column iot.mall_orders.created_at is '创建时间';

comment on column iot.mall_orders.updated_at is '更新时间';

comment on column iot.mall_orders.buyer_message is '买家留言';

comment on column iot.mall_orders.seller_message is '卖家留言';

comment on column iot.mall_orders.remark is '备注信息';

alter table iot.mall_orders
    owner to postgres;

create index idx_mall_orders_order_no
    on iot.mall_orders (order_no);

create index idx_mall_orders_user_id
    on iot.mall_orders (user_id);

create index idx_mall_orders_status
    on iot.mall_orders (order_status);

create index idx_mall_orders_payment_status
    on iot.mall_orders (payment_status);

create index idx_mall_orders_order_time
    on iot.mall_orders (order_time);

create index idx_mall_orders_payment_no
    on iot.mall_orders (payment_no);

create table iot.mall_order_items
(
    id            bigserial
        primary key,
    order_id      bigint         not null
        constraint fk_mall_order_items_order_id
            references iot.mall_orders,
    order_no      varchar(32)    not null,
    product_id    bigint         not null,
    product_code  varchar(100),
    product_name  varchar(200)   not null,
    product_image varchar(500),
    unit_price    numeric(10, 2) not null,
    quantity      integer        not null,
    total_price   numeric(12, 2) not null,
    created_at    timestamp default CURRENT_TIMESTAMP,
    updated_at    timestamp default CURRENT_TIMESTAMP,
    remark        text
);

comment on table iot.mall_order_items is '订单明细表';

comment on column iot.mall_order_items.id is '主键ID，自增';

comment on column iot.mall_order_items.order_id is '订单ID，关联订单主表';

comment on column iot.mall_order_items.order_no is '订单号（冗余字段，便于查询）';

comment on column iot.mall_order_items.product_id is '商品ID';

comment on column iot.mall_order_items.product_code is '商品编码';

comment on column iot.mall_order_items.product_name is '商品名称';

comment on column iot.mall_order_items.product_image is '商品图片';

comment on column iot.mall_order_items.unit_price is '商品单价';

comment on column iot.mall_order_items.quantity is '购买数量';

comment on column iot.mall_order_items.total_price is '小计金额（单价*数量）';

comment on column iot.mall_order_items.created_at is '创建时间';

comment on column iot.mall_order_items.updated_at is '更新时间';

comment on column iot.mall_order_items.remark is '备注信息';

alter table iot.mall_order_items
    owner to postgres;

create index idx_mall_order_items_order_id
    on iot.mall_order_items (order_id);

create index idx_mall_order_items_order_no
    on iot.mall_order_items (order_no);

create index idx_mall_order_items_product_id
    on iot.mall_order_items (product_id);

