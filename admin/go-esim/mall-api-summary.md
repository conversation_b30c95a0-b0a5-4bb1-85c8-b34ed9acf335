# 商城系统API接口文档

## 概述

基于DDL分析，为商城系统设计了完整的后台管理API接口，包含商品管理、分类管理、订单管理和收货地址管理四大核心模块。

## 技术规范

- **框架**: go-zero
- **字段命名**: 驼峰命名法（camelCase）
- **时间格式**: int64时间戳
- **分页结构**: 使用BaseListInfo和BaseMsgResp模式
- **中文注释**: 所有字段都包含中文说明

## API模块设计

### 1. 商品管理模块 (Product Management)

**路径前缀**: `/mall/product`

#### 核心功能
- 商品CRUD操作
- 批量状态更新
- 多维度筛选和搜索
- 支持商品类型：PHYSICAL(实物)、VIRTUAL(虚拟)、SERVICE(服务)

#### 主要接口

| 方法 | 路径 | 功能 | Handler |
|------|------|------|---------|
| POST | `/create` | 创建商品 | CreateProduct |
| PUT | `/update` | 更新商品 | UpdateProduct |
| GET | `/list` | 获取商品列表 | GetProductList |
| GET | `/detail/:id` | 获取商品详情 | GetProductDetail |
| DELETE | `/delete` | 删除商品 | DeleteProduct |
| PUT | `/batch/status` | 批量更新状态 | BatchUpdateProductStatus |

#### 查询条件（按重要程度排序）
1. `keyword` - 关键词搜索（商品名称、编码）
2. `categoryId` - 分类ID筛选
3. `status` - 商品状态筛选
4. `isPublished` - 是否发布筛选
5. `brandId` - 品牌ID筛选
6. `productType` - 商品类型筛选
7. `isFeatured` - 是否推荐筛选
8. `isGift` - 是否为赠品筛选
9. `minPrice/maxPrice` - 价格区间筛选
10. `startTime/endTime` - 时间区间筛选
11. `sortBy` - 排序字段（createdAt/salePrice/sortOrder）
12. `sortOrder` - 排序方式（asc/desc）

### 2. 分类管理模块 (Category Management)

**路径前缀**: `/mall/category`

#### 核心功能
- 多级分类管理
- 分类树结构
- 分类移动操作
- 支持层级显示控制

#### 主要接口

| 方法 | 路径 | 功能 | Handler |
|------|------|------|---------|
| POST | `/create` | 创建分类 | CreateCategory |
| PUT | `/update` | 更新分类 | UpdateCategory |
| GET | `/list` | 获取分类列表 | GetCategoryList |
| GET | `/tree` | 获取分类树 | GetCategoryTree |
| GET | `/detail/:id` | 获取分类详情 | GetCategoryDetail |
| DELETE | `/delete` | 删除分类 | DeleteCategory |
| PUT | `/move` | 移动分类 | MoveCategory |

#### 查询条件（按重要程度排序）
1. `parentId` - 父分类ID筛选
2. `status` - 分类状态筛选
3. `level` - 分类级别筛选
4. `isVisible` - 是否显示筛选
5. `keyword` - 关键词搜索（分类名称、编码）
6. `includeChildren` - 是否包含子分类
7. `maxLevel` - 最大层级（分类树专用）

### 3. 订单管理模块 (Order Management)

**路径前缀**: `/mall/order`

#### 核心功能
- 订单状态管理
- 发货信息更新
- 订单取消操作
- 多维度订单查询

#### 主要接口

| 方法 | 路径 | 功能 | Handler |
|------|------|------|---------|
| GET | `/list` | 获取订单列表 | GetOrderList |
| GET | `/detail/:id` | 获取订单详情 | GetOrderDetail |
| PUT | `/status` | 更新订单状态 | UpdateOrderStatus |
| PUT | `/shipping` | 更新发货信息 | UpdateShippingInfo |
| PUT | `/cancel` | 取消订单 | CancelOrder |

#### 查询条件（按重要程度排序）
1. `orderNo` - 订单号筛选
2. `orderStatus` - 订单状态筛选
3. `paymentStatus` - 支付状态筛选
4. `userId` - 用户ID筛选
5. `consigneeName` - 收货人姓名筛选
6. `consigneePhone` - 收货人手机筛选
7. `shippingStatus` - 发货状态筛选
8. `orderType` - 订单类型筛选
9. `paymentMethod` - 支付方式筛选
10. `minAmount/maxAmount` - 金额区间筛选
11. `startTime/endTime` - 时间区间筛选
12. `keyword` - 关键词搜索（订单号、收货人信息）

### 4. 收货地址管理模块 (Shipping Address Management)

**路径前缀**: `/mall/address`

#### 核心功能
- 用户地址管理
- 默认地址设置
- 地址有效性控制
- 地理位置支持

#### 主要接口

| 方法 | 路径 | 功能 | Handler |
|------|------|------|---------|
| POST | `/create` | 创建收货地址 | CreateShippingAddress |
| PUT | `/update` | 更新收货地址 | UpdateShippingAddress |
| GET | `/list` | 获取地址列表 | GetShippingAddressList |
| GET | `/detail/:id` | 获取地址详情 | GetShippingAddressDetail |
| DELETE | `/delete` | 删除收货地址 | DeleteShippingAddress |
| PUT | `/default` | 设置默认地址 | SetDefaultAddress |

#### 查询条件（按重要程度排序）
1. `userId` - 用户ID筛选
2. `isDefault` - 是否默认地址筛选
3. `isActive` - 是否有效筛选
4. `addressLabel` - 地址标签筛选（HOME/COMPANY/OTHER）
5. `provinceName` - 省份名称筛选
6. `cityName` - 城市名称筛选
7. `keyword` - 关键词搜索（收货人姓名、手机号、地址）

## 数据结构说明

### 分页响应结构
```go
type XXXListInfo {
    BaseListInfo
    Data []XXXItem `json:"data"`
}

type XXXListResp {
    BaseMsgResp
    Data XXXListInfo `json:"data"`
}
```

### 基础响应结构
```go
type BaseMsgResp {
    Code int    `json:"code"`    // 错误代码
    Msg  string `json:"msg"`     // 提示信息
}

type BaseListInfo {
    Total uint64 `json:"total"`  // 数据总数
    Data string `json:"data,omitempty"` // 数据
}
```

## 状态枚举值

### 商品状态 (Product Status)
- `DRAFT` - 草稿
- `ACTIVE` - 上架
- `INACTIVE` - 下架
- `DELETED` - 删除

### 订单状态 (Order Status)
- `PENDING` - 待付款
- `PAID` - 已付款
- `SHIPPED` - 已发货
- `DELIVERED` - 已送达
- `COMPLETED` - 已完成
- `CANCELLED` - 已取消

### 支付状态 (Payment Status)
- `UNPAID` - 未支付
- `PAID` - 已支付
- `REFUNDED` - 已退款

### 发货状态 (Shipping Status)
- `UNSHIPPED` - 未发货
- `SHIPPED` - 已发货
- `DELIVERED` - 已送达

## 前端开发建议

1. **分页处理**: 统一使用BaseListInfo结构处理分页数据
2. **状态管理**: 建议创建状态枚举常量文件
3. **时间处理**: 所有时间字段为int64时间戳，需要前端格式化
4. **搜索功能**: 支持关键词搜索和多条件筛选组合
5. **错误处理**: 统一使用BaseMsgResp结构处理响应

## 后续扩展

API设计预留了扩展空间，可根据业务需求添加：
- 商品SKU管理
- 优惠券系统
- 库存管理
- 评价系统
- 统计报表

---

**文档版本**: v1.0  
**创建时间**: 2025-06-22  
**API文件**: `/desc/mall.api`
