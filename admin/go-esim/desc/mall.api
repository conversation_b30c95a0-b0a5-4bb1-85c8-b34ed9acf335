syntax = "v1"
import "base.api"

// =================== 商品管理 ===================
type Product {
    Id int64 `json:"id"`                                    // 商品ID
    ProductCode string `json:"productCode"`                // 商品编码
    ProductName string `json:"productName"`                // 商品名称
    ProductTitle string `json:"productTitle"`              // 商品标题
    ShortDescription string `json:"shortDescription"`      // 商品简介
    Description string `json:"description"`                // 商品详细描述
    CategoryId int64 `json:"categoryId"`                   // 分类ID
    CategoryPath string `json:"categoryPath"`              // 分类路径
    BrandId int64 `json:"brandId"`                         // 品牌ID
    BrandName string `json:"brandName"`                    // 品牌名称
    ProductType string `json:"productType"`                // 商品类型：PHYSICAL-实物，VIRTUAL-虚拟，SERVICE-服务
    Model string `json:"model"`                            // 型号
    Weight float64 `json:"weight"`                         // 重量（千克）
    Dimensions string `json:"dimensions"`                  // 尺寸
    MarketPrice float64 `json:"marketPrice"`               // 市场价
    SalePrice float64 `json:"salePrice"`                   // 销售价
    CostPrice float64 `json:"costPrice"`                   // 成本价
    HasSku bool `json:"hasSku"`                            // 是否有SKU
    MinOrderQuantity int32 `json:"minOrderQuantity"`       // 最小起订量
    MaxOrderQuantity int32 `json:"maxOrderQuantity"`       // 最大限购量
    IsGift bool `json:"isGift"`                            // 是否为赠品
    Status string `json:"status"`                          // 商品状态：DRAFT-草稿，ACTIVE-上架，INACTIVE-下架，DELETED-删除
    IsPublished bool `json:"isPublished"`                  // 是否发布
    IsFeatured bool `json:"isFeatured"`                    // 是否推荐
    SortOrder int32 `json:"sortOrder"`                     // 排序权重
    MainImage string `json:"mainImage"`                    // 主图URL
    LaunchTime int64 `json:"launchTime"`                   // 上架时间
    CreatedAt int64 `json:"createdAt"`                     // 创建时间
    UpdatedAt int64 `json:"updatedAt"`                     // 更新时间
    Remark string `json:"remark"`                          // 备注信息
}

type CreateProductReq {
    ProductCode string `json:"productCode"`                // 商品编码，必填
    ProductName string `json:"productName"`                // 商品名称，必填
    ProductTitle string `json:"productTitle,optional"`     // 商品标题，可选
    ShortDescription string `json:"shortDescription,optional"` // 商品简介，可选
    Description string `json:"description,optional"`       // 商品详细描述，可选
    CategoryId int64 `json:"categoryId"`                   // 分类ID，必填
    BrandId int64 `json:"brandId,optional"`                // 品牌ID，可选
    BrandName string `json:"brandName,optional"`           // 品牌名称，可选
    ProductType string `json:"productType,default=PHYSICAL"` // 商品类型，默认PHYSICAL
    Model string `json:"model,optional"`                   // 型号，可选
    Weight float64 `json:"weight,optional"`                // 重量，可选
    Dimensions string `json:"dimensions,optional"`         // 尺寸，可选
    MarketPrice float64 `json:"marketPrice,optional"`      // 市场价，可选
    SalePrice float64 `json:"salePrice"`                   // 销售价，必填
    CostPrice float64 `json:"costPrice,optional"`          // 成本价，可选
    HasSku bool `json:"hasSku,default=false"`              // 是否有SKU，默认false
    MinOrderQuantity int32 `json:"minOrderQuantity,default=1"` // 最小起订量，默认1
    MaxOrderQuantity int32 `json:"maxOrderQuantity,optional"` // 最大限购量，可选
    IsGift bool `json:"isGift,default=false"`              // 是否为赠品，默认false
    SortOrder int32 `json:"sortOrder,default=0"`           // 排序权重，默认0
    MainImage string `json:"mainImage,optional"`           // 主图URL，可选
    LaunchTime int64 `json:"launchTime,optional"`          // 上架时间，可选
    Remark string `json:"remark,optional"`                 // 备注信息，可选
}

type CreateProductResp {
    BaseMsgResp
    Data CreateProductData `json:"data"`                   // 创建成功返回的数据
}

type CreateProductData {
    Id int64 `json:"id"`                                   // 新创建的商品ID
    ProductCode string `json:"productCode"`               // 商品编码
}

type UpdateProductReq {
    Id int64 `json:"id"`                                   // 商品ID，必填
    ProductName string `json:"productName,optional"`      // 商品名称，可选
    ProductTitle string `json:"productTitle,optional"`    // 商品标题，可选
    ShortDescription string `json:"shortDescription,optional"` // 商品简介，可选
    Description string `json:"description,optional"`      // 商品详细描述，可选
    CategoryId int64 `json:"categoryId,optional"`         // 分类ID，可选
    BrandId int64 `json:"brandId,optional"`               // 品牌ID，可选
    BrandName string `json:"brandName,optional"`          // 品牌名称，可选
    ProductType string `json:"productType,optional"`      // 商品类型，可选
    Model string `json:"model,optional"`                  // 型号，可选
    Weight float64 `json:"weight,optional"`               // 重量，可选
    Dimensions string `json:"dimensions,optional"`        // 尺寸，可选
    MarketPrice float64 `json:"marketPrice,optional"`     // 市场价，可选
    SalePrice float64 `json:"salePrice,optional"`         // 销售价，可选
    CostPrice float64 `json:"costPrice,optional"`         // 成本价，可选
    HasSku bool `json:"hasSku,optional"`                  // 是否有SKU，可选
    MinOrderQuantity int32 `json:"minOrderQuantity,optional"` // 最小起订量，可选
    MaxOrderQuantity int32 `json:"maxOrderQuantity,optional"` // 最大限购量，可选
    IsGift bool `json:"isGift,optional"`                  // 是否为赠品，可选
    Status string `json:"status,optional"`                // 商品状态，可选
    IsPublished bool `json:"isPublished,optional"`        // 是否发布，可选
    IsFeatured bool `json:"isFeatured,optional"`          // 是否推荐，可选
    SortOrder int32 `json:"sortOrder,optional"`           // 排序权重，可选
    MainImage string `json:"mainImage,optional"`          // 主图URL，可选
    LaunchTime int64 `json:"launchTime,optional"`         // 上架时间，可选
    Remark string `json:"remark,optional"`                // 备注信息，可选
}

type UpdateProductResp {
    BaseMsgResp                                            // 更新操作响应
}

type GetProductListReq {
    Page int64 `form:"page,default=1"`                     // 页码，默认第1页
    PageSize int64 `form:"pageSize,default=20"`           // 每页数量，默认20条
    Keyword string `form:"keyword,optional"`               // 搜索关键词，支持商品名称、编码搜索
    CategoryId int64 `form:"categoryId,optional"`          // 分类ID筛选
    BrandId int64 `form:"brandId,optional"`                // 品牌ID筛选
    ProductType string `form:"productType,optional"`       // 商品类型筛选
    Status string `form:"status,optional"`                 // 商品状态筛选
    IsPublished bool `form:"isPublished,optional"`         // 是否发布筛选
    IsFeatured bool `form:"isFeatured,optional"`           // 是否推荐筛选
    IsGift bool `form:"isGift,optional"`                   // 是否为赠品筛选
    MinPrice float64 `form:"minPrice,optional"`            // 最低价格筛选
    MaxPrice float64 `form:"maxPrice,optional"`            // 最高价格筛选
    StartTime int64 `form:"startTime,optional"`            // 开始时间筛选
    EndTime int64 `form:"endTime,optional"`                // 结束时间筛选
    SortBy string `form:"sortBy,default=createdAt"`        // 排序字段：createdAt-创建时间，salePrice-价格，sortOrder-权重
    SortOrder string `form:"sortOrder,default=desc"`       // 排序方式：asc-升序，desc-降序
}

type ProductListInfo {
    BaseListInfo
    Data []Product `json:"data"`                           // 商品列表
}

type GetProductListResp {
    BaseMsgResp
    Data ProductListInfo `json:"data"`                     // 商品列表数据
}

type GetProductDetailReq {
    Id int64 `path:"id"`                                   // 商品ID，路径参数
}

type GetProductDetailResp {
    BaseMsgResp
    Data Product `json:"data"`                             // 商品详细信息
}

type DeleteProductReq {
    Id int64 `json:"id"`                                   // 商品ID，必填
}

type DeleteProductResp {
    BaseMsgResp                                            // 删除操作响应
}

type BatchUpdateProductStatusReq {
    Ids []int64 `json:"ids"`                              // 商品ID列表，必填
    Status string `json:"status"`                         // 目标状态，必填
}

type BatchUpdateProductStatusResp {
    BaseMsgResp                                            // 批量更新操作响应
}

// =================== 分类管理 ===================
type ProductCategory {
    Id int64 `json:"id"`                                   // 分类ID
    CategoryCode string `json:"categoryCode"`             // 分类编码
    CategoryName string `json:"categoryName"`             // 分类名称
    CategoryAlias string `json:"categoryAlias"`           // 分类别名
    Description string `json:"description"`               // 分类描述
    ParentId int64 `json:"parentId"`                      // 父分类ID
    Level int32 `json:"level"`                            // 分类级别
    Path string `json:"path"`                             // 分类路径
    PathName string `json:"pathName"`                     // 分类名称路径
    Icon string `json:"icon"`                             // 分类图标URL
    Image string `json:"image"`                           // 分类图片URL
    SortOrder int32 `json:"sortOrder"`                    // 排序权重
    Status string `json:"status"`                         // 分类状态：ACTIVE-启用，INACTIVE-禁用，DELETED-删除
    IsLeaf bool `json:"isLeaf"`                           // 是否为叶子节点
    IsVisible bool `json:"isVisible"`                     // 是否前台显示
    ProductCount int32 `json:"productCount"`              // 商品数量
    ChildrenCount int32 `json:"childrenCount"`            // 子分类数量
    CreatedAt int64 `json:"createdAt"`                    // 创建时间
    UpdatedAt int64 `json:"updatedAt"`                    // 更新时间
    Remark string `json:"remark"`                         // 备注信息
    Children []ProductCategory `json:"children,omitempty"` // 子分类列表，可选
}

type CreateCategoryReq {
    CategoryCode string `json:"categoryCode"`             // 分类编码，必填
    CategoryName string `json:"categoryName"`             // 分类名称，必填
    CategoryAlias string `json:"categoryAlias,optional"`  // 分类别名，可选
    Description string `json:"description,optional"`      // 分类描述，可选
    ParentId int64 `json:"parentId,default=0"`            // 父分类ID，默认0（顶级分类）
    Icon string `json:"icon,optional"`                    // 分类图标URL，可选
    Image string `json:"image,optional"`                  // 分类图片URL，可选
    SortOrder int32 `json:"sortOrder,default=0"`          // 排序权重，默认0
    IsVisible bool `json:"isVisible,default=true"`        // 是否前台显示，默认true
    Remark string `json:"remark,optional"`                // 备注信息，可选
}

type CreateCategoryResp {
    BaseMsgResp
    Data CreateCategoryData `json:"data"`                 // 创建成功返回的数据
}

type CreateCategoryData {
    Id int64 `json:"id"`                                  // 新创建的分类ID
    CategoryCode string `json:"categoryCode"`            // 分类编码
}

type UpdateCategoryReq {
    Id int64 `json:"id"`                                  // 分类ID，必填
    CategoryName string `json:"categoryName,optional"`   // 分类名称，可选
    CategoryAlias string `json:"categoryAlias,optional"` // 分类别名，可选
    Description string `json:"description,optional"`     // 分类描述，可选
    Icon string `json:"icon,optional"`                   // 分类图标URL，可选
    Image string `json:"image,optional"`                 // 分类图片URL，可选
    SortOrder int32 `json:"sortOrder,optional"`          // 排序权重，可选
    Status string `json:"status,optional"`               // 分类状态，可选
    IsVisible bool `json:"isVisible,optional"`           // 是否前台显示，可选
    Remark string `json:"remark,optional"`               // 备注信息，可选
}

type UpdateCategoryResp {
    BaseMsgResp                                           // 更新操作响应
}

type GetCategoryListReq {
    Page int64 `form:"page,default=1"`                    // 页码，默认第1页
    PageSize int64 `form:"pageSize,default=20"`          // 每页数量，默认20条
    ParentId int64 `form:"parentId,optional"`             // 父分类ID筛选，可选
    Level int32 `form:"level,optional"`                   // 分类级别筛选，可选
    Status string `form:"status,optional"`                // 分类状态筛选，可选
    IsVisible bool `form:"isVisible,optional"`            // 是否显示筛选，可选
    Keyword string `form:"keyword,optional"`              // 搜索关键词，支持分类名称、编码搜索
    IncludeChildren bool `form:"includeChildren,default=false"` // 是否包含子分类，默认false
}

type CategoryListInfo {
    BaseListInfo
    Data []ProductCategory `json:"data"`                  // 分类列表
}

type GetCategoryListResp {
    BaseMsgResp
    Data CategoryListInfo `json:"data"`                   // 分类列表数据
}

type GetCategoryTreeReq {
    ParentId int64 `form:"parentId,default=0"`            // 父分类ID，默认0（获取完整树）
    MaxLevel int32 `form:"maxLevel,default=3"`            // 最大层级，默认3级
    Status string `form:"status,default=ACTIVE"`          // 分类状态筛选，默认ACTIVE
    IsVisible bool `form:"isVisible,default=true"`        // 是否显示筛选，默认true
}

type GetCategoryTreeResp {
    BaseMsgResp
    Data []ProductCategory `json:"data"`                  // 分类树数据
}

type GetCategoryDetailReq {
    Id int64 `path:"id"`                                  // 分类ID，路径参数
}

type GetCategoryDetailResp {
    BaseMsgResp
    Data ProductCategory `json:"data"`                    // 分类详细信息
}

type DeleteCategoryReq {
    Id int64 `json:"id"`                                  // 分类ID，必填
    ForceDelete bool `json:"forceDelete,default=false"`   // 是否强制删除（包含子分类），默认false
}

type DeleteCategoryResp {
    BaseMsgResp                                           // 删除操作响应
}

type MoveCategoryReq {
    Id int64 `json:"id"`                                  // 分类ID，必填
    NewParentId int64 `json:"newParentId"`                // 新父分类ID，必填
    NewSortOrder int32 `json:"newSortOrder,optional"`     // 新排序权重，可选
}

type MoveCategoryResp {
    BaseMsgResp                                           // 移动操作响应
}

// =================== 订单管理 ===================
type Order {
    Id int64 `json:"id"`                                  // 订单ID
    OrderNo string `json:"orderNo"`                      // 订单号
    UserId int64 `json:"userId"`                         // 用户ID
    OrderType string `json:"orderType"`                  // 订单类型：NORMAL-普通订单，PRESALE-预售，GROUP-团购
    TotalAmount float64 `json:"totalAmount"`             // 订单总金额
    ProductAmount float64 `json:"productAmount"`         // 商品总金额
    FreightAmount float64 `json:"freightAmount"`         // 运费金额
    DiscountAmount float64 `json:"discountAmount"`       // 优惠金额
    CouponAmount float64 `json:"couponAmount"`           // 优惠券金额
    ActualAmount float64 `json:"actualAmount"`           // 实付金额
    ConsigneeName string `json:"consigneeName"`          // 收货人姓名
    ConsigneePhone string `json:"consigneePhone"`        // 收货人手机号
    ShippingProvince string `json:"shippingProvince"`    // 收货省份
    ShippingCity string `json:"shippingCity"`            // 收货城市
    ShippingDistrict string `json:"shippingDistrict"`    // 收货区县
    ShippingAddress string `json:"shippingAddress"`      // 收货详细地址
    OrderStatus string `json:"orderStatus"`              // 订单状态：PENDING-待付款，PAID-已付款，SHIPPED-已发货，DELIVERED-已送达，COMPLETED-已完成，CANCELLED-已取消
    PaymentStatus string `json:"paymentStatus"`          // 支付状态：UNPAID-未支付，PAID-已支付，REFUNDED-已退款
    ShippingStatus string `json:"shippingStatus"`        // 发货状态：UNSHIPPED-未发货，SHIPPED-已发货，DELIVERED-已送达
    PaymentMethod string `json:"paymentMethod"`          // 支付方式：ALIPAY-支付宝，WECHAT-微信，BANK-银行卡
    PaymentNo string `json:"paymentNo"`                  // 支付流水号
    PaidAt int64 `json:"paidAt"`                         // 支付时间
    LogisticsCompany string `json:"logisticsCompany"`    // 物流公司
    TrackingNo string `json:"trackingNo"`                // 物流单号
    ShippedAt int64 `json:"shippedAt"`                   // 发货时间
    DeliveredAt int64 `json:"deliveredAt"`               // 签收时间
    OrderTime int64 `json:"orderTime"`                   // 下单时间
    ExpireTime int64 `json:"expireTime"`                 // 订单过期时间
    CreatedAt int64 `json:"createdAt"`                   // 创建时间
    UpdatedAt int64 `json:"updatedAt"`                   // 更新时间
    BuyerMessage string `json:"buyerMessage"`            // 买家留言
    SellerMessage string `json:"sellerMessage"`          // 卖家留言
    Remark string `json:"remark"`                        // 备注信息
    OrderItems []OrderItem `json:"orderItems,omitempty"` // 订单明细列表，可选
}

type OrderItem {
    Id int64 `json:"id"`                                  // 订单明细ID
    OrderId int64 `json:"orderId"`                       // 订单ID
    OrderNo string `json:"orderNo"`                      // 订单号
    ProductId int64 `json:"productId"`                   // 商品ID
    ProductCode string `json:"productCode"`              // 商品编码
    ProductName string `json:"productName"`              // 商品名称
    ProductImage string `json:"productImage"`            // 商品图片
    UnitPrice float64 `json:"unitPrice"`                 // 商品单价
    Quantity int32 `json:"quantity"`                     // 购买数量
    TotalPrice float64 `json:"totalPrice"`               // 小计金额
    CreatedAt int64 `json:"createdAt"`                   // 创建时间
    UpdatedAt int64 `json:"updatedAt"`                   // 更新时间
    Remark string `json:"remark"`                        // 备注信息
}

type GetOrderListReq {
    Page int64 `form:"page,default=1"`                    // 页码，默认第1页
    PageSize int64 `form:"pageSize,default=20"`          // 每页数量，默认20条
    OrderNo string `form:"orderNo,optional"`             // 订单号筛选，可选
    UserId int64 `form:"userId,optional"`                // 用户ID筛选，可选
    OrderType string `form:"orderType,optional"`         // 订单类型筛选，可选
    OrderStatus string `form:"orderStatus,optional"`     // 订单状态筛选，可选
    PaymentStatus string `form:"paymentStatus,optional"` // 支付状态筛选，可选
    ShippingStatus string `form:"shippingStatus,optional"` // 发货状态筛选，可选
    PaymentMethod string `form:"paymentMethod,optional"` // 支付方式筛选，可选
    ConsigneeName string `form:"consigneeName,optional"` // 收货人姓名筛选，可选
    ConsigneePhone string `form:"consigneePhone,optional"` // 收货人手机筛选，可选
    MinAmount float64 `form:"minAmount,optional"`        // 最小金额筛选，可选
    MaxAmount float64 `form:"maxAmount,optional"`        // 最大金额筛选，可选
    StartTime int64 `form:"startTime,optional"`          // 开始时间筛选，可选
    EndTime int64 `form:"endTime,optional"`              // 结束时间筛选，可选
    Keyword string `form:"keyword,optional"`             // 搜索关键词，支持订单号、收货人姓名、手机号搜索
}

type OrderListInfo {
    BaseListInfo
    Data []Order `json:"data"`                           // 订单列表
}

type GetOrderListResp {
    BaseMsgResp
    Data OrderListInfo `json:"data"`                     // 订单列表数据
}

type GetOrderDetailReq {
    Id int64 `path:"id"`                                 // 订单ID，路径参数
}

type GetOrderDetailResp {
    BaseMsgResp
    Data Order `json:"data"`                            // 订单详细信息，包含明细
}

type UpdateOrderStatusReq {
    Id int64 `json:"id"`                                 // 订单ID，必填
    OrderStatus string `json:"orderStatus"`             // 订单状态，必填
    SellerMessage string `json:"sellerMessage,optional"` // 卖家留言，可选
    Remark string `json:"remark,optional"`              // 备注信息，可选
}

type UpdateOrderStatusResp {
    BaseMsgResp                                          // 更新操作响应
}

type UpdateShippingInfoReq {
    Id int64 `json:"id"`                                 // 订单ID，必填
    LogisticsCompany string `json:"logisticsCompany"`   // 物流公司，必填
    TrackingNo string `json:"trackingNo"`               // 物流单号，必填
    ShippedAt int64 `json:"shippedAt,optional"`         // 发货时间，可选（默认当前时间）
    Remark string `json:"remark,optional"`              // 备注信息，可选
}

type UpdateShippingInfoResp {
    BaseMsgResp                                          // 更新操作响应
}

type CancelOrderReq {
    Id int64 `json:"id"`                                 // 订单ID，必填
    CancelReason string `json:"cancelReason"`           // 取消原因，必填
    Remark string `json:"remark,optional"`              // 备注信息，可选
}

type CancelOrderResp {
    BaseMsgResp                                          // 取消操作响应
}

// =================== 收货地址管理 ===================
type ShippingAddress {
    Id int64 `json:"id"`                                 // 地址ID
    UserId int64 `json:"userId"`                        // 用户ID
    ConsigneeName string `json:"consigneeName"`         // 收货人姓名
    ConsigneePhone string `json:"consigneePhone"`       // 收货人手机号
    ConsigneeTel string `json:"consigneeTel"`           // 收货人固定电话
    ProvinceId int32 `json:"provinceId"`                // 省份ID
    ProvinceName string `json:"provinceName"`           // 省份名称
    CityId int32 `json:"cityId"`                        // 城市ID
    CityName string `json:"cityName"`                   // 城市名称
    DistrictId int32 `json:"districtId"`                // 区县ID
    DistrictName string `json:"districtName"`           // 区县名称
    AddressDetail string `json:"addressDetail"`         // 详细地址
    PostalCode string `json:"postalCode"`               // 邮政编码
    Longitude float64 `json:"longitude"`                // 经度
    Latitude float64 `json:"latitude"`                  // 纬度
    AddressLabel string `json:"addressLabel"`           // 地址标签：HOME-家，COMPANY-公司，OTHER-其他
    IsDefault bool `json:"isDefault"`                   // 是否默认地址
    IsActive bool `json:"isActive"`                     // 是否有效
    CreatedAt int64 `json:"createdAt"`                  // 创建时间
    UpdatedAt int64 `json:"updatedAt"`                  // 更新时间
    LastUsedAt int64 `json:"lastUsedAt"`                // 最后使用时间
    Remark string `json:"remark"`                       // 备注信息
}

type CreateShippingAddressReq {
    UserId int64 `json:"userId"`                        // 用户ID，必填
    ConsigneeName string `json:"consigneeName"`         // 收货人姓名，必填
    ConsigneePhone string `json:"consigneePhone"`       // 收货人手机号，必填
    ConsigneeTel string `json:"consigneeTel,optional"`  // 收货人固定电话，可选
    ProvinceId int32 `json:"provinceId"`                // 省份ID，必填
    ProvinceName string `json:"provinceName"`           // 省份名称，必填
    CityId int32 `json:"cityId"`                        // 城市ID，必填
    CityName string `json:"cityName"`                   // 城市名称，必填
    DistrictId int32 `json:"districtId,optional"`       // 区县ID，可选
    DistrictName string `json:"districtName,optional"`  // 区县名称，可选
    AddressDetail string `json:"addressDetail"`         // 详细地址，必填
    PostalCode string `json:"postalCode,optional"`      // 邮政编码，可选
    Longitude float64 `json:"longitude,optional"`       // 经度，可选
    Latitude float64 `json:"latitude,optional"`         // 纬度，可选
    AddressLabel string `json:"addressLabel,default=OTHER"` // 地址标签，默认OTHER
    IsDefault bool `json:"isDefault,default=false"`     // 是否默认地址，默认false
    Remark string `json:"remark,optional"`              // 备注信息，可选
}

type CreateShippingAddressResp {
    BaseMsgResp
    Data CreateShippingAddressData `json:"data"`        // 创建成功返回的数据
}

type CreateShippingAddressData {
    Id int64 `json:"id"`                                // 新创建的地址ID
}

type UpdateShippingAddressReq {
    Id int64 `json:"id"`                                // 地址ID，必填
    ConsigneeName string `json:"consigneeName,optional"` // 收货人姓名，可选
    ConsigneePhone string `json:"consigneePhone,optional"` // 收货人手机号，可选
    ConsigneeTel string `json:"consigneeTel,optional"`  // 收货人固定电话，可选
    ProvinceId int32 `json:"provinceId,optional"`       // 省份ID，可选
    ProvinceName string `json:"provinceName,optional"`  // 省份名称，可选
    CityId int32 `json:"cityId,optional"`               // 城市ID，可选
    CityName string `json:"cityName,optional"`          // 城市名称，可选
    DistrictId int32 `json:"districtId,optional"`       // 区县ID，可选
    DistrictName string `json:"districtName,optional"`  // 区县名称，可选
    AddressDetail string `json:"addressDetail,optional"` // 详细地址，可选
    PostalCode string `json:"postalCode,optional"`      // 邮政编码，可选
    Longitude float64 `json:"longitude,optional"`       // 经度，可选
    Latitude float64 `json:"latitude,optional"`         // 纬度，可选
    AddressLabel string `json:"addressLabel,optional"`  // 地址标签，可选
    IsDefault bool `json:"isDefault,optional"`          // 是否默认地址，可选
    IsActive bool `json:"isActive,optional"`            // 是否有效，可选
    Remark string `json:"remark,optional"`              // 备注信息，可选
}

type UpdateShippingAddressResp {
    BaseMsgResp                                          // 更新操作响应
}

type GetShippingAddressListReq {
    Page int64 `form:"page,default=1"`                   // 页码，默认第1页
    PageSize int64 `form:"pageSize,default=20"`         // 每页数量，默认20条
    UserId int64 `form:"userId,optional"`               // 用户ID筛选，可选
    AddressLabel string `form:"addressLabel,optional"`  // 地址标签筛选，可选
    IsDefault bool `form:"isDefault,optional"`          // 是否默认地址筛选，可选
    IsActive bool `form:"isActive,optional"`            // 是否有效筛选，可选
    ProvinceName string `form:"provinceName,optional"`  // 省份名称筛选，可选
    CityName string `form:"cityName,optional"`          // 城市名称筛选，可选
    Keyword string `form:"keyword,optional"`            // 搜索关键词，支持收货人姓名、手机号、地址搜索
}

type ShippingAddressListInfo {
    BaseListInfo
    Data []ShippingAddress `json:"data"`                // 收货地址列表
}

type GetShippingAddressListResp {
    BaseMsgResp
    Data ShippingAddressListInfo `json:"data"`          // 收货地址列表数据
}

type GetShippingAddressDetailReq {
    Id int64 `path:"id"`                                // 地址ID，路径参数
}

type GetShippingAddressDetailResp {
    BaseMsgResp
    Data ShippingAddress `json:"data"`                 // 收货地址详细信息
}

type DeleteShippingAddressReq {
    Id int64 `json:"id"`                                // 地址ID，必填
}

type DeleteShippingAddressResp {
    BaseMsgResp                                         // 删除操作响应
}

type SetDefaultAddressReq {
    Id int64 `json:"id"`                                // 地址ID，必填
    UserId int64 `json:"userId"`                       // 用户ID，必填
}

type SetDefaultAddressResp {
    BaseMsgResp                                         // 设置操作响应
}

// =================== API服务定义 ===================

// 商品管理服务
@server(
    group: product
    prefix: /mall/product
)
service Esim {
    @doc "创建商品"
    @handler CreateProduct
    post /create (CreateProductReq) returns (CreateProductResp)

    @doc "更新商品"
    @handler UpdateProduct
    put /update (UpdateProductReq) returns (UpdateProductResp)

    @doc "获取商品列表"
    @handler GetProductList
    get /list (GetProductListReq) returns (GetProductListResp)

    @doc "获取商品详情"
    @handler GetProductDetail
    get /detail/:id (GetProductDetailReq) returns (GetProductDetailResp)

    @doc "删除商品"
    @handler DeleteProduct
    delete /delete (DeleteProductReq) returns (DeleteProductResp)

    @doc "批量更新商品状态"
    @handler BatchUpdateProductStatus
    put /batch/status (BatchUpdateProductStatusReq) returns (BatchUpdateProductStatusResp)
}

// 分类管理服务
@server(
    group: category
    prefix: /mall/category
)
service Esim {
    @doc "创建分类"
    @handler CreateCategory
    post /create (CreateCategoryReq) returns (CreateCategoryResp)

    @doc "更新分类"
    @handler UpdateCategory
    put /update (UpdateCategoryReq) returns (UpdateCategoryResp)

    @doc "获取分类列表"
    @handler GetCategoryList
    get /list (GetCategoryListReq) returns (GetCategoryListResp)

    @doc "获取分类树"
    @handler GetCategoryTree
    get /tree (GetCategoryTreeReq) returns (GetCategoryTreeResp)

    @doc "获取分类详情"
    @handler GetCategoryDetail
    get /detail/:id (GetCategoryDetailReq) returns (GetCategoryDetailResp)

    @doc "删除分类"
    @handler DeleteCategory
    delete /delete (DeleteCategoryReq) returns (DeleteCategoryResp)

    @doc "移动分类"
    @handler MoveCategory
    put /move (MoveCategoryReq) returns (MoveCategoryResp)
}

// 订单管理服务
@server(
    group: order
    prefix: /mall/order
)
service Esim {
    @doc "获取订单列表"
    @handler GetOrderList
    get /list (GetOrderListReq) returns (GetOrderListResp)

    @doc "获取订单详情"
    @handler GetOrderDetail
    get /detail/:id (GetOrderDetailReq) returns (GetOrderDetailResp)

    @doc "更新订单状态"
    @handler UpdateOrderStatus
    put /status (UpdateOrderStatusReq) returns (UpdateOrderStatusResp)

    @doc "更新发货信息"
    @handler UpdateShippingInfo
    put /shipping (UpdateShippingInfoReq) returns (UpdateShippingInfoResp)

    @doc "取消订单"
    @handler CancelOrder
    put /cancel (CancelOrderReq) returns (CancelOrderResp)
}

// 收货地址管理服务
@server(
    group: address
    prefix: /mall/address
)
service Esim {
    @doc "创建收货地址"
    @handler CreateShippingAddress
    post /create (CreateShippingAddressReq) returns (CreateShippingAddressResp)

    @doc "更新收货地址"
    @handler UpdateShippingAddress
    put /update (UpdateShippingAddressReq) returns (UpdateShippingAddressResp)

    @doc "获取收货地址列表"
    @handler GetShippingAddressList
    get /list (GetShippingAddressListReq) returns (GetShippingAddressListResp)

    @doc "获取收货地址详情"
    @handler GetShippingAddressDetail
    get /detail/:id (GetShippingAddressDetailReq) returns (GetShippingAddressDetailResp)

    @doc "删除收货地址"
    @handler DeleteShippingAddress
    delete /delete (DeleteShippingAddressReq) returns (DeleteShippingAddressResp)

    @doc "设置默认地址"
    @handler SetDefaultAddress
    put /default (SetDefaultAddressReq) returns (SetDefaultAddressResp)
}
