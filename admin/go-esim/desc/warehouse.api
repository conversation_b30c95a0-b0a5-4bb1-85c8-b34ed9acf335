syntax = "v1"
import "base.api"

type Warehouse {
    Id int64 `json:"id"`                           // 云仓ID，主键
    WarehouseCode string `json:"warehouseCode"`   // 云仓编码，唯一标识
    WarehouseName string `json:"warehouseName"`   // 云仓名称
    CompanyName string `json:"companyName"`       // 云仓公司名称
    ContactPerson string `json:"contactPerson"`   // 联系人姓名
    ContactPhone string `json:"contactPhone"`     // 联系电话
    ContactEmail string `json:"contactEmail"`     // 联系邮箱
    Address string `json:"address"`                // 云仓详细地址
    Province string `json:"province"`              // 省份
    City string `json:"city"`                      // 城市
    District string `json:"district"`              // 区县
    ApiEndpoint string `json:"apiEndpoint"`       // 奇门API接口地址
    AppKey string `json:"appKey"`                 // 奇门API应用密钥
    CustomerID string `json:"customerId"`         // 奇门API客户ID
    Status int32 `json:"status"`                   // 云仓状态：1-正常 2-停用
    CreatedAt int64 `json:"createdAt"`           // 创建时间
    UpdatedAt int64 `json:"updatedAt"`           // 更新时间
}

type GetWarehouseListReq {
    Page int64 `form:"page,default=1"`             // 页码，默认第1页
    PageSize int64 `form:"pageSize,default=20"`   // 每页数量，默认20条
    Keyword string `form:"keyword,optional"`       // 搜索关键词，可选，支持云仓名称、编码搜索
    Status int32 `form:"status,optional"`          // 状态筛选，可选：1-正常 2-停用
}

type WarehouseListData {
    BaseListInfo
    Data []Warehouse `json:"data"`                 // 云仓列表
}

type GetWarehouseListResp {
    BaseMsgResp
    Data WarehouseListData `json:"data"`           // 云仓列表数据
}

type GetWarehouseDetailReq {
    Id int64 `path:"id"`                           // 云仓ID，路径参数
}

type GetWarehouseDetailResp {
    BaseMsgResp
    Data Warehouse `json:"data"`                   // 云仓详细信息
}

type CreateWarehouseReq {
    WarehouseCode string `json:"warehouseCode"`   // 云仓编码，必填，全局唯一
    WarehouseName string `json:"warehouseName"`   // 云仓名称，必填
    CompanyName string `json:"companyName"`       // 云仓公司名称，必填
    ContactPerson string `json:"contactPerson"`   // 联系人姓名，必填
    ContactPhone string `json:"contactPhone"`     // 联系电话，必填
    ContactEmail string `json:"contactEmail,optional"` // 联系邮箱，可选
    Address string `json:"address"`                // 云仓详细地址，必填
    Province string `json:"province"`              // 省份，必填
    City string `json:"city"`                      // 城市，必填
    District string `json:"district,optional"`     // 区县，可选
    ApiEndpoint string `json:"apiEndpoint"`       // 奇门API接口地址，必填
    AppKey string `json:"appKey"`                 // 奇门API应用密钥，必填
    AppSecret string `json:"appSecret"`           // 奇门API应用密钥，必填
    CustomerID string `json:"customerId"`         // 奇门API客户ID，必填
}

type CreateWarehouseResp {
    BaseMsgResp
    Data CreateWarehouseData `json:"data"`         // 创建成功返回的数据
}
type CreateWarehouseData {
    Id int64 `json:"id"`                           // 新创建的云仓ID
}

type UpdateWarehouseReq {
    Id int64 `json:"id"`                           // 云仓ID，必填
    WarehouseName string `json:"warehouseName,optional"`     // 云仓名称，可选
    CompanyName string `json:"companyName,optional"`         // 云仓公司名称，可选
    ContactPerson string `json:"contactPerson,optional"`     // 联系人姓名，可选
    ContactPhone string `json:"contactPhone,optional"`       // 联系电话，可选
    ContactEmail string `json:"contactEmail,optional"`       // 联系邮箱，可选
    Address string `json:"address,optional"`                  // 云仓详细地址，可选
    Province string `json:"province,optional"`                // 省份，可选
    City string `json:"city,optional"`                        // 城市，可选
    District string `json:"district,optional"`                // 区县，可选
    ApiEndpoint string `json:"apiEndpoint,optional"`         // 奇门API接口地址，可选
    AppKey string `json:"appKey,optional"`                   // 奇门API应用密钥，可选
    AppSecret string `json:"appSecret,optional"`             // 奇门API应用密钥，可选
    CustomerID string `json:"customerId,optional"`           // 奇门API客户ID，可选
    Status int32 `json:"status,optional"`                     // 云仓状态，可选：1-正常 2-停用
}

type UpdateWarehouseResp {
    BaseMsgResp                                       // 更新操作响应，无额外数据
}

// =================== 入库管理 ===================
type EntryOrder {
    Id int64 `json:"id"`                                       // 入库单ID，主键
    EntryOrderCode string `json:"entryOrderCode"`           // 入库单号，唯一标识
    WarehouseCode string `json:"warehouseCode"`              // 云仓编码
    WarehouseName string `json:"warehouseName"`              // 云仓名称
    OwnerCode string `json:"ownerCode"`                      // 货主编码，默认NIUYI
    OrderType string `json:"orderType"`                      // 业务类型：SCRK-生产入库 CGRK-采购入库 DBRK-调拨入库
    PurchaseOrderCode string `json:"purchaseOrderCode"`     // 采购单号，当orderType=CGRK时使用
    ExpectStartTime string `json:"expectStartTime"`         // 预期到货开始时间
    ExpectEndTime string `json:"expectEndTime"`             // 预期到货结束时间
    ActualArrivalTime string `json:"actualArrivalTime"`     // 实际到货时间
    LogisticsCode string `json:"logisticsCode"`              // 物流公司编码：SF-顺丰 YTO-圆通等
    LogisticsName string `json:"logisticsName"`              // 物流公司名称
    ExpressCode string `json:"expressCode"`                  // 运单号
    SupplierCode string `json:"supplierCode"`                // 供应商编码（设备厂商编码）
    SupplierName string `json:"supplierName"`                // 供应商名称（设备厂商名称）
    OperatorCode string `json:"operatorCode"`                // 操作员编码
    OperatorName string `json:"operatorName"`                // 操作员姓名
    OperateTime string `json:"operateTime"`                  // 操作时间
    TotalOrderLines int32 `json:"totalOrderLines"`          // 入库单行数统计
    ExpectedQuantity int32 `json:"expectedQuantity"`         // 预期入库数量
    ActualQuantity int32 `json:"actualQuantity"`             // 实际入库数量
    OrderStatus int32 `json:"orderStatus"`                   // 订单状态：1-待入库 2-部分入库 3-全部入库 4-异常 5-取消
    Remark string `json:"remark"`                             // 备注信息
    QimenEntryOrderID string `json:"qimenEntryOrderId"`    // 奇门系统返回的入库单ID
    ApiSyncStatus int32 `json:"apiSyncStatus"`              // API同步状态：0-未同步 1-已同步 2-同步失败
    ApiSyncTime string `json:"apiSyncTime"`                 // API同步时间
    ApiErrorMessage string `json:"apiErrorMessage"`         // API同步错误信息
    CreatedAt int64 `json:"createdAt"`                      // 创建时间
    UpdatedAt int64 `json:"updatedAt"`                      // 更新时间
    OrderLines []EntryOrderLine `json:"orderLines,omitempty"` // 入库单明细列表，可选
}

type EntryOrderLine {
    Id int64 `json:"id"`                               // 入库单明细ID，主键
    EntryOrderID int64 `json:"entryOrderId"`        // 入库单ID，外键
    EntryOrderCode string `json:"entryOrderCode"`   // 入库单号
    OrderLineNo string `json:"orderLineNo"`         // 入库单行号
    OutBizCode string `json:"outBizCode"`           // 外部业务编码，用于去重
    OwnerCode string `json:"ownerCode"`              // 货主编码
    ItemCode string `json:"itemCode"`                // 商品编码（设备序列号）
    ItemID string `json:"itemId"`                    // 仓储系统商品ID
    ItemName string `json:"itemName"`                // 商品名称（设备名称）
    SkuProperty string `json:"skuProperty"`          // 商品属性
    PlanQty int32 `json:"planQty"`                   // 计划入库数量
    ActualQty int32 `json:"actualQty"`               // 实际入库数量
    PurchasePrice float64 `json:"purchasePrice"`     // 采购价格
    RetailPrice float64 `json:"retailPrice"`         // 零售价格
    InventoryType string `json:"inventoryType"`      // 库存类型：ZP-正品 CC-残次 JS-机损 XS-箱损
    BatchCode string `json:"batchCode"`              // 批次编码
    ProduceCode string `json:"produceCode"`          // 生产批号
    ProductDate string `json:"productDate"`          // 商品生产日期 YYYY-MM-DD
    ExpireDate string `json:"expireDate"`            // 商品过期日期 YYYY-MM-DD
    BoxNumber string `json:"boxNumber"`              // 箱号
    PalletNumber string `json:"palletNumber"`        // 卡板号
    Unit string `json:"unit"`                         // 单位：台/个/盒/箱等
    SnCodes []string `json:"snCodes"`                // SN编码列表
    ShelfLocation string `json:"shelfLocation"`      // 上架位置
    InboundTime string `json:"inboundTime"`          // 实际入库时间
    DeviceStatus int32 `json:"deviceStatus"`         // 设备状态：1-正常 2-损坏 3-缺失配件
    Remark string `json:"remark"`                     // 备注
    CreatedAt int64 `json:"createdAt"`              // 创建时间
}

type CreateEntryOrderReq {
    WarehouseCode string `json:"warehouseCode"`              // 云仓编码，必填
    OrderType string `json:"orderType,default=CGRK"`        // 业务类型，默认采购入库
    PurchaseOrderCode string `json:"purchaseOrderCode,optional"` // 采购单号，可选
    ExpectStartTime string `json:"expectStartTime,optional"`     // 预期到货开始时间，可选
    ExpectEndTime string `json:"expectEndTime,optional"`         // 预期到货结束时间，可选
    LogisticsCode string `json:"logisticsCode,optional"`          // 物流公司编码，可选
    LogisticsName string `json:"logisticsName,optional"`          // 物流公司名称，可选
    ExpressCode string `json:"expressCode,optional"`              // 运单号，可选
    SupplierCode string `json:"supplierCode"`                     // 供应商编码，必填
    SupplierName string `json:"supplierName"`                     // 供应商名称，必填
    OperatorName string `json:"operatorName,optional"`            // 操作员姓名，可选
    Remark string `json:"remark,optional"`                         // 备注，可选
    OrderLines []CreateEntryOrderLineReq `json:"orderLines"`      // 入库单明细列表，必填
}

type CreateEntryOrderLineReq {
    ItemCode string `json:"itemCode"`                        // 商品编码（设备序列号），必填
    ItemName string `json:"itemName"`                        // 商品名称，必填
    PlanQty int32 `json:"planQty,default=1"`                 // 计划入库数量，默认1
    PurchasePrice float64 `json:"purchasePrice,optional"`    // 采购价格，可选
    RetailPrice float64 `json:"retailPrice,optional"`        // 零售价格，可选
    BatchCode string `json:"batchCode,optional"`             // 批次编码，可选
    ProduceCode string `json:"produceCode,optional"`         // 生产批号，可选
    ProductDate string `json:"productDate,optional"`         // 生产日期，可选
    ExpireDate string `json:"expireDate,optional"`           // 过期日期，可选
    BoxNumber string `json:"boxNumber,optional"`             // 箱号，可选
    PalletNumber string `json:"palletNumber,optional"`       // 卡板号，可选
    SnCodes []string `json:"snCodes,optional"`               // SN编码列表，可选
    Remark string `json:"remark,optional"`                    // 备注，可选
}

type CreateEntryOrderResp {
    BaseMsgResp
    Data CreateEntryOrderData `json:"data"`                   // 创建成功返回的数据
}

type CreateEntryOrderData {
    Id int64 `json:"id"`                                      // 新创建的入库单ID
    EntryOrderCode string `json:"entryOrderCode"`          // 生成的入库单号
}

type GetEntryOrderListReq {
    Page int64 `form:"page,default=1"`                        // 页码，默认第1页
    PageSize int64 `form:"pageSize,default=20"`              // 每页数量，默认20条
    WarehouseCode string `form:"warehouseCode,optional"`     // 云仓编码筛选，可选
    OrderStatus int32 `form:"orderStatus,optional"`          // 订单状态筛选，可选
    SupplierCode string `form:"supplierCode,optional"`       // 供应商编码筛选，可选
    StartTime string `form:"startTime,optional"`             // 开始时间筛选，可选
    EndTime string `form:"endTime,optional"`                 // 结束时间筛选，可选
    Keyword string `form:"keyword,optional"`                  // 关键词搜索，可选，支持入库单号、供应商名称
}

type GetEntryOrderListResp {
    BaseMsgResp
    Data EntryOrderListData `json:"data"`                     // 入库单列表数据
}

type EntryOrderListData {
    List []EntryOrder `json:"list"`                           // 入库单列表
    PageInfo PageInfo `json:"page"`                      // 分页信息
}

type GetEntryOrderDetailReq {
    Id int64 `path:"id"`                                      // 入库单ID，路径参数
}

type GetEntryOrderDetailResp {
    BaseMsgResp
    Data EntryOrder `json:"data"`                             // 入库单详细信息，包含明细
}

type ConfirmEntryOrderReq {
    Id int64 `json:"id"`                                      // 入库单ID，必填
    ActualArrivalTime string `json:"actualArrivalTime,optional"` // 实际到货时间，可选
    OperatorName string `json:"operatorName,optional"`            // 操作员姓名，可选
    Remark string `json:"remark,optional"`                         // 确认备注，可选
    OrderLines []ConfirmEntryOrderLineReq `json:"orderLines,optional"` // 入库明细确认，可选
}

type ConfirmEntryOrderLineReq {
    Id int64 `json:"id"`                                      // 入库单明细ID，必填
    ActualQty int32 `json:"actualQty"`                       // 实际入库数量，必填
    ShelfLocation string `json:"shelfLocation,optional"`     // 上架位置，可选
    DeviceStatus int32 `json:"deviceStatus,optional"`        // 设备状态，可选
    Remark string `json:"remark,optional"`                    // 明细备注，可选
}

type ConfirmEntryOrderResp {
    BaseMsgResp                                                   // 确认操作响应
}

type CancelEntryOrderReq {
    Id int64 `json:"id"`                                      // 入库单ID，必填
    CancelReason string `json:"cancelReason"`                // 取消原因，必填
    OperatorName string `json:"operatorName,optional"`       // 操作员姓名，可选
}

type CancelEntryOrderResp {
    BaseMsgResp                                                   // 取消操作响应
}

type BatchCreateEntryLinesReq {
    EntryOrderId int64 `json:"entryOrderId"`               // 入库单ID，必填
    OrderLines []CreateEntryOrderLineReq `json:"orderLines"` // 批量入库明细，必填
}

type BatchCreateEntryLinesResp {
    BaseMsgResp
    Data BatchCreateEntryLinesData `json:"data"`              // 批量创建结果
}

type BatchCreateEntryLinesData {
    SuccessCount int32 `json:"successCount"`                 // 成功创建数量
    FailCount int32 `json:"failCount"`                       // 失败数量
    FailDetails []string `json:"failDetails,omitempty"`      // 失败详情列表
}

// =================== 出库管理 ===================
type DeliveryOrder {
    Id int64 `json:"id"`                                       // 出库单ID，主键
    DeliveryOrderCode string `json:"deliveryOrderCode"`     // 出库单号，唯一标识
    WarehouseCode string `json:"warehouseCode"`              // 云仓编码
    WarehouseName string `json:"warehouseName"`              // 云仓名称
    OwnerCode string `json:"ownerCode"`                      // 货主编码，默认NIUYI
    OrderType string `json:"orderType"`                      // 业务类型：JYCK-交易出库 DBCK-调拨出库
    OrderCreateTime string `json:"orderCreateTime"`         // 订单创建时间
    Priority int32 `json:"priority"`                          // 优先级：1-普通 2-紧急 3-特急
    ReceiverName string `json:"receiverName"`                // 收货人姓名
    ReceiverPhone string `json:"receiverPhone"`              // 收货人电话
    ReceiverMobile string `json:"receiverMobile"`            // 收货人手机
    ReceiverEmail string `json:"receiverEmail"`              // 收货人邮箱
    ReceiverIdType string `json:"receiverIdType"`           // 收件人证件类型：1-身份证 2-军官证 3-护照 4-其他
    ReceiverIdNumber string `json:"receiverIdNumber"`       // 收件人证件号码
    ReceiverProvince string `json:"receiverProvince"`        // 收货省份
    ReceiverCity string `json:"receiverCity"`                // 收货城市
    ReceiverArea string `json:"receiverArea"`                // 收货区域
    ReceiverTown string `json:"receiverTown"`                // 收货村镇
    ReceiverAddress string `json:"receiverAddress"`          // 收货详细地址
    ReceiverZipCode string `json:"receiverZipCode"`         // 收货邮编
    RecipientType int32 `json:"recipientType"`               // 收货人类型：1-合伙人 2-代理商 3-用户
    RecipientId int64 `json:"recipientId"`                   // 收货人ID
    TradeOrderCode string `json:"tradeOrderCode"`           // 交易订单号
    ExpectStartTime string `json:"expectStartTime"`         // 预期发货开始时间
    ExpectEndTime string `json:"expectEndTime"`             // 预期发货结束时间
    LogisticsCode string `json:"logisticsCode"`              // 物流公司编码
    LogisticsName string `json:"logisticsName"`              // 物流公司名称
    ExpressCode string `json:"expressCode"`                  // 运单号
    ExpressFee float64 `json:"expressFee"`                   // 快递费用
    TotalOrderLines int32 `json:"totalOrderLines"`          // 出库单行数统计
    RequestedQuantity int32 `json:"requestedQuantity"`       // 申请出库数量
    ActualQuantity int32 `json:"actualQuantity"`             // 实际出库数量
    OrderStatus int32 `json:"orderStatus"`                   // 订单状态：1-待出库 2-已出库 3-已发货 4-已收货 5-异常 6-取消
    ShipTime string `json:"shipTime"`                        // 发货时间
    ReceiveTime string `json:"receiveTime"`                  // 收货时间
    OperatorCode string `json:"operatorCode"`                // 操作员编码
    OperatorName string `json:"operatorName"`                // 操作员姓名
    OperateTime string `json:"operateTime"`                  // 操作时间
    Remark string `json:"remark"`                             // 备注
    QimenDeliveryOrderID string `json:"qimenDeliveryOrderId"` // 奇门系统返回的出库单ID
    ApiSyncStatus int32 `json:"apiSyncStatus"`              // API同步状态：0-未同步 1-已同步 2-同步失败
    ApiSyncTime string `json:"apiSyncTime"`                 // API同步时间
    ApiErrorMessage string `json:"apiErrorMessage"`         // API同步错误信息
    CreatedAt int64 `json:"createdAt"`                      // 创建时间
    UpdatedAt int64 `json:"updatedAt"`                      // 更新时间
    OrderLines []DeliveryOrderLine `json:"orderLines,omitempty"` // 出库单明细列表，可选
}

type DeliveryOrderLine {
    Id int64 `json:"id"`                                       // 出库单明细ID，主键
    DeliveryOrderID int64 `json:"deliveryOrderId"`          // 出库单ID，外键
    DeliveryOrderCode string `json:"deliveryOrderCode"`     // 出库单号
    OrderLineNo string `json:"orderLineNo"`                 // 出库单行号
    OutBizCode string `json:"outBizCode"`                   // 外部业务编码
    OwnerCode string `json:"ownerCode"`                      // 货主编码
    ItemCode string `json:"itemCode"`                        // 商品编码（设备序列号）
    ItemID string `json:"itemId"`                            // 仓储系统商品ID
    ItemName string `json:"itemName"`                        // 商品名称
    SkuProperty string `json:"skuProperty"`                  // 商品属性
    PlanQty int32 `json:"planQty"`                           // 计划出库数量
    ActualQty int32 `json:"actualQty"`                       // 实际出库数量
    RetailPrice float64 `json:"retailPrice"`                 // 零售价格
    InventoryType string `json:"inventoryType"`              // 库存类型
    BatchCode string `json:"batchCode"`                      // 批次编码
    ProduceCode string `json:"produceCode"`                  // 生产批号
    OriginalShelfLocation string `json:"originalShelfLocation"` // 原货架位置
    OriginalBoxNumber string `json:"originalBoxNumber"`     // 原箱号
    OriginalPalletNumber string `json:"originalPalletNumber"` // 原卡板号
    Unit string `json:"unit"`                                 // 单位
    SnCodes []string `json:"snCodes"`                        // SN编码列表
    OutboundTime string `json:"outboundTime"`                // 实际出库时间
    Remark string `json:"remark"`                             // 备注
    CreatedAt int64 `json:"createdAt"`                      // 创建时间
}

// 出库相关请求响应结构继续...
type CreateDeliveryOrderReq {
    WarehouseCode string `json:"warehouseCode"`              // 云仓编码，必填
    OrderType string `json:"orderType,default=JYCK"`        // 业务类型，默认交易出库
    Priority int32 `json:"priority,default=1"`                // 优先级，默认普通
    ReceiverName string `json:"receiverName"`                // 收货人姓名，必填
    ReceiverPhone string `json:"receiverPhone"`              // 收货人电话，必填
    ReceiverMobile string `json:"receiverMobile,optional"`   // 收货人手机，可选
    ReceiverEmail string `json:"receiverEmail,optional"`     // 收货人邮箱，可选
    ReceiverIdType string `json:"receiverIdType,optional"`  // 收件人证件类型，可选
    ReceiverIdNumber string `json:"receiverIdNumber,optional"` // 收件人证件号码，可选
    ReceiverProvince string `json:"receiverProvince"`        // 收货省份，必填
    ReceiverCity string `json:"receiverCity"`                // 收货城市，必填
    ReceiverArea string `json:"receiverArea,optional"`       // 收货区域，可选
    ReceiverTown string `json:"receiverTown,optional"`       // 收货村镇，可选
    ReceiverAddress string `json:"receiverAddress"`          // 收货详细地址，必填
    ReceiverZipCode string `json:"receiverZipCode,optional"` // 收货邮编，可选
    RecipientType int32 `json:"recipientType"`               // 收货人类型，必填：1-合伙人 2-代理商 3-用户
    RecipientId int64 `json:"recipientId"`                   // 收货人ID，必填
    TradeOrderCode string `json:"tradeOrderCode,optional"`  // 交易订单号，可选
    ExpectStartTime string `json:"expectStartTime,optional"` // 预期发货开始时间，可选
    ExpectEndTime string `json:"expectEndTime,optional"`     // 预期发货结束时间，可选
    LogisticsCode string `json:"logisticsCode,optional"`     // 物流公司编码，可选
    LogisticsName string `json:"logisticsName,optional"`     // 物流公司名称，可选
    OperatorName string `json:"operatorName,optional"`       // 操作员姓名，可选
    Remark string `json:"remark,optional"`                    // 备注，可选
    OrderLines []CreateDeliveryOrderLineReq `json:"orderLines"` // 出库单明细列表，必填
}

type CreateDeliveryOrderLineReq {
    ItemCode string `json:"itemCode"`                        // 商品编码（设备序列号），必填
    ItemName string `json:"itemName"`                        // 商品名称，必填
    PlanQty int32 `json:"planQty,default=1"`                 // 计划出库数量，默认1
    BatchCode string `json:"batchCode,optional"`             // 指定批次编码，可选
    Remark string `json:"remark,optional"`                    // 备注，可选
}

type CreateDeliveryOrderResp {
    BaseMsgResp
    Data CreateDeliveryOrderData `json:"data"`                // 创建成功返回的数据
}

type CreateDeliveryOrderData {
    Id int64 `json:"id"`                                      // 新创建的出库单ID
    DeliveryOrderCode string `json:"deliveryOrderCode"`    // 生成的出库单号
}

// =================== 库存管理 ===================
type InventoryItem {
    Id int64 `json:"id"`                                      // 库存商品ID，主键
    ItemCode string `json:"itemCode"`                        // 商品编码，唯一标识
    ItemName string `json:"itemName"`                        // 商品名称
    ItemType int32 `json:"itemType"`                         // 商品类型：1-设备 2-卡片 3-其他
    CostPrice float64 `json:"costPrice"`                     // 成本价格
    RetailPrice float64 `json:"retailPrice"`                // 零售价格
    Unit string `json:"unit"`                                // 单位：台/张/个等
    Status int32 `json:"status"`                             // 商品状态：1-正常 2-停用
    Remark string `json:"remark"`                            // 备注
    CreatedAt int64 `json:"createdAt"`                      // 创建时间
    UpdatedAt int64 `json:"updatedAt"`                      // 更新时间
}

type Inventory {
    Id int64 `json:"id"`                                      // 库存记录ID，主键
    WarehouseCode string `json:"warehouseCode"`             // 云仓编码
    OwnerCode string `json:"ownerCode"`                     // 货主编码
    ItemCode string `json:"itemCode"`                       // 商品编码（设备序列号）
    ItemID string `json:"itemId"`                           // 仓储系统商品ID
    ItemName string `json:"itemName"`                       // 商品名称
    AvailableQty int32 `json:"availableQty"`                // 可用库存数量
    LockedQty int32 `json:"lockedQty"`                      // 锁定库存数量
    PickedQty int32 `json:"pickedQty"`                      // 拣货库存数量
    TotalQty int32 `json:"totalQty"`                        // 总库存数量
    InventoryType string `json:"inventoryType"`             // 库存类型：ZP-正品 CC-残次等
    InventoryStatus int32 `json:"inventoryStatus"`          // 库存状态：1-在库 2-预出库 3-已出库 4-损坏 5-丢失
    BatchCode string `json:"batchCode"`                     // 批次编码
    ProduceCode string `json:"produceCode"`                 // 生产批号
    ProductDate string `json:"productDate"`                 // 生产日期
    ExpireDate string `json:"expireDate"`                   // 过期日期
    ShelfLocation string `json:"shelfLocation"`             // 货架位置
    BoxNumber string `json:"boxNumber"`                     // 箱号
    PalletNumber string `json:"palletNumber"`               // 卡板号
    InWarehouseTime string `json:"inWarehouseTime"`        // 入库时间
    OutWarehouseTime string `json:"outWarehouseTime"`      // 出库时间
    LastInventoryTime string `json:"lastInventoryTime"`    // 最后盘点时间
    InBatchNo string `json:"inBatchNo"`                    // 入库批次号
    OutBatchNo string `json:"outBatchNo"`                  // 出库批次号
    Remark string `json:"remark"`                            // 备注
    CreatedAt int64 `json:"createdAt"`                     // 创建时间
    UpdatedAt int64 `json:"updatedAt"`                     // 更新时间
}

type CreateInventoryItemReq {
    ItemCode string `json:"itemCode"`                        // 商品编码，必填，全局唯一
    ItemName string `json:"itemName"`                        // 商品名称，必填
    ItemType int32 `json:"itemType,default=1"`               // 商品类型，默认1：1-设备 2-卡片 3-其他
    CostPrice float64 `json:"costPrice,optional"`           // 成本价格，可选
    RetailPrice float64 `json:"retailPrice,optional"`       // 零售价格，可选
    Unit string `json:"unit,default=台"`                     // 单位，默认"台"
    Remark string `json:"remark,optional"`                   // 备注，可选
}

type CreateInventoryItemResp {
    BaseMsgResp
    Data CreateInventoryItemData `json:"data"`               // 创建成功返回的数据
}

type CreateInventoryItemData {
    Id int64 `json:"id"`                                     // 新创建的库存商品ID
    ItemCode string `json:"itemCode"`                       // 商品编码
}

type GetInventoryItemListReq {
    Page int64 `form:"page,default=1"`                       // 页码，默认第1页
    PageSize int64 `form:"pageSize,default=20"`             // 每页数量，默认20条
    ItemType int32 `form:"itemType,optional"`               // 商品类型筛选，可选：1-设备 2-卡片 3-其他
    ItemCode string `form:"itemCode,optional"`              // 商品编码筛选，可选
    ItemName string `form:"itemName,optional"`              // 商品名称筛选，可选
    Status int32 `form:"status,optional"`                   // 商品状态筛选，可选
    Keyword string `form:"keyword,optional"`                 // 关键词搜索，可选，支持商品编码、名称搜索
}

type GetInventoryItemListResp {
    BaseMsgResp
    Data InventoryItemListData `json:"data"`                // 库存商品列表数据
}

type InventoryItemListData {
    BaseListInfo
    Data []InventoryItem `json:"data"`                      // 库存商品列表
}

type GetInventoryItemDetailReq {
    Id int64 `path:"id"`                                     // 库存商品ID，路径参数
}

type GetInventoryItemDetailResp {
    BaseMsgResp
    Data InventoryItem `json:"data"`                        // 库存商品详细信息
}

type UpdateInventoryItemReq {
    Id int64 `json:"id"`                                     // 库存商品ID，必填
    ItemName string `json:"itemName,optional"`              // 商品名称，可选
    ItemType int32 `json:"itemType,optional"`               // 商品类型，可选：1-设备 2-卡片 3-其他
    CostPrice float64 `json:"costPrice,optional"`           // 成本价格，可选
    RetailPrice float64 `json:"retailPrice,optional"`       // 零售价格，可选
    Unit string `json:"unit,optional"`                      // 单位，可选
    Status int32 `json:"status,optional"`                   // 商品状态，可选：1-正常 2-停用
    Remark string `json:"remark,optional"`                   // 备注，可选
}

type UpdateInventoryItemResp {
    BaseMsgResp                                               // 更新操作响应，无额外数据
}

type QueryInventoryReq {
    Page int64 `form:"page,default=1"`                       // 页码，默认第1页
    PageSize int64 `form:"pageSize,default=20"`             // 每页数量，默认20条
    WarehouseCode string `form:"warehouseCode,optional"`    // 云仓编码筛选，可选
    ItemCode string `form:"itemCode,optional"`              // 商品编码筛选，可选
    InventoryStatus int32 `form:"inventoryStatus,optional"` // 库存状态筛选，可选
    InventoryType string `form:"inventoryType,optional"`    // 库存类型筛选，可选
    BatchCode string `form:"batchCode,optional"`            // 批次编码筛选，可选
    ShelfLocation string `form:"shelfLocation,optional"`    // 货架位置筛选，可选
    MinQty int32 `form:"minQty,optional"`                   // 最小库存数量筛选，可选
    MaxQty int32 `form:"maxQty,optional"`                   // 最大库存数量筛选，可选
}

type QueryInventoryResp {
    BaseMsgResp
    Data InventoryListData `json:"data"`                     // 库存查询结果
}

type InventoryListData {
    List []Inventory `json:"list"`                           // 库存列表
    PageInfo PageInfo `json:"page"`                     // 分页信息
    Summary InventorySummary `json:"summary"`                // 库存汇总信息
}

type InventorySummary {
    TotalItems int64 `json:"totalItems"`                    // 总商品种类数
    TotalQty int64 `json:"totalQty"`                        // 总库存数量
    AvailableQty int64 `json:"availableQty"`                // 总可用库存
    LockedQty int64 `json:"lockedQty"`                      // 总锁定库存
}

// =================== API同步日志 ===================
type QimenSyncLog {
    Id int64 `json:"id"`                                      // 同步日志ID，主键
    WarehouseCode string `json:"warehouseCode"`             // 云仓编码
    ApiMethod string `json:"apiMethod"`                     // API方法名
    SyncType int32 `json:"syncType"`                        // 同步类型：1-入库单 2-出库单 3-库存查询 4-库存同步
    SyncDirection int32 `json:"syncDirection"`              // 同步方向：1-推送到云仓 2-从云仓拉取
    RelatedOrderId int64 `json:"relatedOrderId"`           // 关联订单ID
    RelatedOrderCode string `json:"relatedOrderCode"`      // 关联订单号
    SyncStatus int32 `json:"syncStatus"`                    // 同步状态：1-成功 2-失败 3-部分成功
    ErrorCode string `json:"errorCode"`                     // 错误码
    ErrorMessage string `json:"errorMessage"`               // 错误信息
    RetryCount int32 `json:"retryCount"`                    // 重试次数
    MaxRetryCount int32 `json:"maxRetryCount"`             // 最大重试次数
    NextRetryTime string `json:"nextRetryTime"`            // 下次重试时间
    SyncTime string `json:"syncTime"`                       // 同步时间
    ResponseTime int32 `json:"responseTime"`                // 响应时间（毫秒）
    CreatedAt int64 `json:"createdAt"`                     // 创建时间
}

type GetQimenSyncLogsReq {
    Page int64 `form:"page,default=1"`                       // 页码，默认第1页
    PageSize int64 `form:"pageSize,default=20"`             // 每页数量，默认20条
    WarehouseCode string `form:"warehouseCode,optional"`    // 云仓编码筛选，可选
    SyncType int32 `form:"syncType,optional"`               // 同步类型筛选，可选
    SyncStatus int32 `form:"syncStatus,optional"`           // 同步状态筛选，可选
    StartTime string `form:"startTime,optional"`            // 开始时间筛选，可选
    EndTime string `form:"endTime,optional"`                // 结束时间筛选，可选
    RelatedOrderCode string `form:"relatedOrderCode,optional"` // 关联订单号筛选，可选
}

type GetQimenSyncLogsResp {
    BaseMsgResp
    Data QimenSyncLogListData `json:"data"`                  // 同步日志列表数据
}

type QimenSyncLogListData {
    List []QimenSyncLog `json:"list"`                        // 同步日志列表
    PageInfo PageInfo `json:"page"`                     // 分页信息
}

// =================== 同步操作请求响应 ===================
type SyncEntryOrderToQimenReq {
    EntryOrderId int64 `json:"entryOrderId"`               // 入库单ID，必填
    ForceSync bool `json:"forceSync,default=false"`         // 是否强制同步，默认false
}

type SyncEntryOrderToQimenResp {
    BaseMsgResp
    Data SyncResultData `json:"data"`                        // 同步结果数据
}

type SyncResultData {
    SyncLogId int64 `json:"syncLogId"`                     // 同步日志ID
    QimenOrderId string `json:"qimenOrderId,omitempty"`    // 奇门系统订单ID
    SyncStatus int32 `json:"syncStatus"`                    // 同步状态
    Message string `json:"message"`                          // 同步结果消息
}

type SyncDeliveryOrderToQimenReq {
    DeliveryOrderId int64 `json:"deliveryOrderId"`         // 出库单ID，必填
    ForceSync bool `json:"forceSync,default=false"`         // 是否强制同步，默认false
}

type SyncDeliveryOrderToQimenResp {
    BaseMsgResp
    Data SyncResultData `json:"data"`                        // 同步结果数据
}

type SyncInventoryFromQimenReq {
    WarehouseCode string `json:"warehouseCode"`             // 云仓编码，必填
    ItemCodes []string `json:"itemCodes,optional"`          // 指定商品编码列表，可选，为空则同步所有
}

type SyncInventoryFromQimenResp {
    BaseMsgResp
    Data InventorySyncResultData `json:"data"`               // 库存同步结果
}

type InventorySyncResultData {
    SyncLogId int64 `json:"syncLogId"`                     // 同步日志ID
    SyncCount int32 `json:"syncCount"`                      // 同步数量
    SuccessCount int32 `json:"successCount"`                // 成功数量
    FailCount int32 `json:"failCount"`                      // 失败数量
    Message string `json:"message"`                          // 同步结果消息
}

type RetryQimenSyncReq {
    SyncLogIds []int64 `json:"syncLogIds"`                 // 同步日志ID列表，必填
}

type RetryQimenSyncResp {
    BaseMsgResp
    Data RetryResultData `json:"data"`                       // 重试结果数据
}

type RetryResultData {
    TotalCount int32 `json:"totalCount"`                    // 总重试数量
    SuccessCount int32 `json:"successCount"`                // 成功数量
    FailCount int32 `json:"failCount"`                      // 失败数量
    Details []RetryDetail `json:"details,omitempty"`         // 重试详情
}

type RetryDetail {
    SyncLogId int64 `json:"syncLogId"`                     // 同步日志ID
    Success bool `json:"success"`                            // 是否成功
    Message string `json:"message"`                          // 结果消息
}

// 出库单列表相关
type GetDeliveryOrderListReq {
    Page int64 `form:"page,default=1"`                        // 页码，默认第1页
    PageSize int64 `form:"pageSize,default=20"`              // 每页数量，默认20条
    WarehouseCode string `form:"warehouseCode,optional"`     // 云仓编码筛选，可选
    OrderStatus int32 `form:"orderStatus,optional"`          // 订单状态筛选，可选
    RecipientType int32 `form:"recipientType,optional"`      // 收货人类型筛选，可选
    StartTime string `form:"startTime,optional"`             // 开始时间筛选，可选
    EndTime string `form:"endTime,optional"`                 // 结束时间筛选，可选
    Keyword string `form:"keyword,optional"`                  // 关键词搜索，可选，支持出库单号、收货人姓名
}

type GetDeliveryOrderListResp {
    BaseMsgResp
    Data DeliveryOrderListData `json:"data"`                  // 出库单列表数据
}

type DeliveryOrderListData {
    List []DeliveryOrder `json:"list"`                        // 出库单列表
    PageInfo PageInfo `json:"page"`                      // 分页信息
}

type GetDeliveryOrderDetailReq {
    Id int64 `path:"id"`                                      // 出库单ID，路径参数
}

type GetDeliveryOrderDetailResp {
    BaseMsgResp
    Data DeliveryOrder `json:"data"`                          // 出库单详细信息，包含明细
}

// 出库确认相关
type ConfirmDeliveryOrderReq {
    Id int64 `json:"id"`                                      // 出库单ID，必填
    OperatorName string `json:"operatorName,optional"`       // 操作员姓名，可选
    Remark string `json:"remark,optional"`                    // 确认备注，可选
    OrderLines []ConfirmDeliveryOrderLineReq `json:"orderLines,optional"` // 出库明细确认，可选
}

type ConfirmDeliveryOrderLineReq {
    Id int64 `json:"id"`                                      // 出库单明细ID，必填
    ActualQty int32 `json:"actualQty"`                       // 实际出库数量，必填
    OriginalShelfLocation string `json:"originalShelfLocation,optional"` // 原货架位置，可选
    Remark string `json:"remark,optional"`                    // 明细备注，可选
}

type ConfirmDeliveryOrderResp {
    BaseMsgResp                                                // 确认操作响应
}

// 发货确认相关
type ShipDeliveryOrderReq {
    Id int64 `json:"id"`                                      // 出库单ID，必填
    LogisticsCode string `json:"logisticsCode"`              // 物流公司编码，必填
    LogisticsName string `json:"logisticsName"`              // 物流公司名称，必填
    ExpressCode string `json:"expressCode"`                  // 运单号，必填
    ExpressFee float64 `json:"expressFee,optional"`          // 快递费用，可选
    ShipTime string `json:"shipTime,optional"`               // 发货时间，可选，默认当前时间
    OperatorName string `json:"operatorName,optional"`       // 操作员姓名，可选
    Remark string `json:"remark,optional"`                    // 发货备注，可选
}

type ShipDeliveryOrderResp {
    BaseMsgResp                                                // 发货操作响应
}

// 收货确认相关
type ReceiveDeliveryOrderReq {
    Id int64 `json:"id"`                                      // 出库单ID，必填
    ReceiveTime string `json:"receiveTime,optional"`         // 收货时间，可选，默认当前时间
    ReceiverName string `json:"receiverName,optional"`       // 实际收货人姓名，可选
    Remark string `json:"remark,optional"`                    // 收货备注，可选
}

type ReceiveDeliveryOrderResp {
    BaseMsgResp                                                // 收货操作响应
}

// 库存相关缺少的类型
type InventoryCheckReq {
    WarehouseCode string `json:"warehouseCode"`              // 云仓编码，必填
    CheckType int32 `json:"checkType,default=1"`             // 盘点类型：1-全盘 2-抽盘 3-循环盘点
    ItemCodes []string `json:"itemCodes,optional"`           // 指定商品编码列表，可选
    ShelfLocations []string `json:"shelfLocations,optional"` // 指定货架位置列表，可选
    OperatorName string `json:"operatorName"`                // 操作员姓名，必填
    Remark string `json:"remark,optional"`                    // 盘点备注，可选
}

type InventoryCheckResp {
    BaseMsgResp
    Data InventoryCheckData `json:"data"`                     // 盘点结果数据
}

type InventoryCheckData {
    CheckId int64 `json:"checkId"`                           // 盘点任务ID
    TotalItems int32 `json:"totalItems"`                     // 盘点商品总数
    CheckedItems int32 `json:"checkedItems"`                 // 已盘点商品数
    DiffItems int32 `json:"diffItems"`                       // 差异商品数
    Status int32 `json:"status"`                              // 盘点状态：1-进行中 2-已完成
}

type AdjustInventoryReq {
    WarehouseCode string `json:"warehouseCode"`              // 云仓编码，必填
    ItemCode string `json:"itemCode"`                        // 商品编码，必填
    AdjustType int32 `json:"adjustType"`                     // 调整类型：1-增加 2-减少
    AdjustQty int32 `json:"adjustQty"`                       // 调整数量，必填
    AdjustReason string `json:"adjustReason"`                // 调整原因，必填
    BatchCode string `json:"batchCode,optional"`             // 批次编码，可选
    OperatorName string `json:"operatorName"`                // 操作员姓名，必填
    Remark string `json:"remark,optional"`                    // 调整备注，可选
}

type AdjustInventoryResp {
    BaseMsgResp
    Data AdjustInventoryData `json:"data"`                    // 调整结果数据
}

type AdjustInventoryData {
    BeforeQty int32 `json:"beforeQty"`                       // 调整前数量
    AfterQty int32 `json:"afterQty"`                         // 调整后数量
    AdjustQty int32 `json:"adjustQty"`                       // 调整数量
}

type GetInventoryLogsReq {
    Page int64 `form:"page,default=1"`                        // 页码，默认第1页
    PageSize int64 `form:"pageSize,default=20"`              // 每页数量，默认20条
    WarehouseCode string `form:"warehouseCode,optional"`     // 云仓编码筛选，可选
    ItemCode string `form:"itemCode,optional"`               // 商品编码筛选，可选
    OperationType int32 `form:"operationType,optional"`      // 操作类型筛选，可选
    StartTime string `form:"startTime,optional"`             // 开始时间筛选，可选
    EndTime string `form:"endTime,optional"`                 // 结束时间筛选，可选
}

type GetInventoryLogsResp {
    BaseMsgResp
    Data InventoryLogListData `json:"data"`                   // 库存日志列表数据
}

type InventoryLogListData {
    List []InventoryLog `json:"list"`                         // 库存日志列表
    PageInfo PageInfo `json:"page"`                      // 分页信息
}

type InventoryLog {
    Id int64 `json:"id"`                                      // 日志ID，主键
    WarehouseCode string `json:"warehouseCode"`             // 云仓编码
    OwnerCode string `json:"ownerCode"`                     // 货主编码
    ItemCode string `json:"itemCode"`                       // 商品编码
    ItemName string `json:"itemName"`                       // 商品名称
    OperationType int32 `json:"operationType"`              // 操作类型：1-入库 2-出库 3-盘点 4-调拨 5-报损 6-报溢
    OperationDesc string `json:"operationDesc"`             // 操作描述
    OperationQuantity int32 `json:"operationQuantity"`      // 操作数量
    BeforeQty int32 `json:"beforeQty"`                      // 操作前数量
    AfterQty int32 `json:"afterQty"`                        // 操作后数量
    RelatedOrderId int64 `json:"relatedOrderId"`           // 关联订单ID
    RelatedOrderCode string `json:"relatedOrderCode"`      // 关联订单号
    RelatedOrderType int32 `json:"relatedOrderType"`       // 关联订单类型
    BatchCode string `json:"batchCode"`                     // 批次编码
    InventoryType string `json:"inventoryType"`             // 库存类型
    OperatorId int64 `json:"operatorId"`                    // 操作员ID
    OperatorName string `json:"operatorName"`               // 操作员姓名
    OperationTime string `json:"operationTime"`             // 操作时间
    Remark string `json:"remark"`                            // 备注
    CreatedAt int64 `json:"createdAt"`                      // 创建时间
}


// =================== 云仓基础信息管理 ===================
@server(
    group: warehouse
    prefix: /warehouse
)
service Esim {
    @doc "获取云仓列表"
    @handler GetWarehouseList
    get /list (GetWarehouseListReq) returns (GetWarehouseListResp)
    
    @doc "获取云仓详情"
    @handler GetWarehouseDetail
    get /detail/:id (GetWarehouseDetailReq) returns (GetWarehouseDetailResp)
    
    @doc "创建云仓"
    @handler CreateWarehouse
    post /create (CreateWarehouseReq) returns (CreateWarehouseResp)
    
    @doc "更新云仓"
    @handler UpdateWarehouse
    put /update (UpdateWarehouseReq) returns (UpdateWarehouseResp)
}

// =================== 入库管理 ===================
@server(
    group: entry
    prefix: /warehouse/entry
)
service Esim {
    @doc "创建入库单"
    @handler CreateEntryOrder
    post /order/create (CreateEntryOrderReq) returns (CreateEntryOrderResp)
    
    @doc "获取入库单列表"
    @handler GetEntryOrderList
    get /order/list (GetEntryOrderListReq) returns (GetEntryOrderListResp)
    
    @doc "获取入库单详情"
    @handler GetEntryOrderDetail
    get /order/detail/:id (GetEntryOrderDetailReq) returns (GetEntryOrderDetailResp)
    
    @doc "确认入库"
    @handler ConfirmEntryOrder
    put /order/confirm (ConfirmEntryOrderReq) returns (ConfirmEntryOrderResp)
    
    @doc "取消入库单"
    @handler CancelEntryOrder
    put /order/cancel (CancelEntryOrderReq) returns (CancelEntryOrderResp)
    
    @doc "批量录入入库明细"
    @handler BatchCreateEntryLines
    post /lines/batch (BatchCreateEntryLinesReq) returns (BatchCreateEntryLinesResp)
}

// =================== 出库管理 ===================
@server(
    group: delivery
    prefix: /warehouse/delivery
)
service Esim {
    @doc "创建出库单"
    @handler CreateDeliveryOrder
    post /order/create (CreateDeliveryOrderReq) returns (CreateDeliveryOrderResp)
    
    @doc "获取出库单列表"
    @handler GetDeliveryOrderList
    get /order/list (GetDeliveryOrderListReq) returns (GetDeliveryOrderListResp)
    
    @doc "获取出库单详情"
    @handler GetDeliveryOrderDetail
    get /order/detail/:id (GetDeliveryOrderDetailReq) returns (GetDeliveryOrderDetailResp)
    
    @doc "确认出库"
    @handler ConfirmDeliveryOrder
    put /order/confirm (ConfirmDeliveryOrderReq) returns (ConfirmDeliveryOrderResp)
    
    @doc "发货确认"
    @handler ShipDeliveryOrder
    put /order/ship (ShipDeliveryOrderReq) returns (ShipDeliveryOrderResp)
    
    @doc "收货确认"
    @handler ReceiveDeliveryOrder
    put /order/receive (ReceiveDeliveryOrderReq) returns (ReceiveDeliveryOrderResp)
}

// =================== 库存管理 ===================
@server(
    group: inventory
    prefix: /warehouse/inventory
)
service Esim {
    @doc "新增库存商品"
    @handler CreateInventoryItem
    post /item/create (CreateInventoryItemReq) returns (CreateInventoryItemResp)

    @doc "获取库存商品列表"
    @handler GetInventoryItemList
    get /item/list (GetInventoryItemListReq) returns (GetInventoryItemListResp)

    @doc "获取库存商品详情"
    @handler GetInventoryItemDetail
    get /item/detail/:id (GetInventoryItemDetailReq) returns (GetInventoryItemDetailResp)

    @doc "查询库存"
    @handler QueryInventory
    get /query (QueryInventoryReq) returns (QueryInventoryResp)

    @doc "库存盘点"
    @handler InventoryCheck
    post /check (InventoryCheckReq) returns (InventoryCheckResp)

    @doc "库存调整"
    @handler AdjustInventory
    post /adjust (AdjustInventoryReq) returns (AdjustInventoryResp)

    @doc "获取库存变动日志"
    @handler GetInventoryLogs
    get /logs (GetInventoryLogsReq) returns (GetInventoryLogsResp)
}

// =================== 奇门API同步 ===================
@server(
    group: qimen
    prefix: /warehouse/qimen
)
service Esim {
    @doc "手动同步入库单到奇门"
    @handler SyncEntryOrderToQimen
    post /sync/entry (SyncEntryOrderToQimenReq) returns (SyncEntryOrderToQimenResp)
    
    @doc "手动同步出库单到奇门"
    @handler SyncDeliveryOrderToQimen
    post /sync/delivery (SyncDeliveryOrderToQimenReq) returns (SyncDeliveryOrderToQimenResp)
    
    @doc "从奇门同步库存"
    @handler SyncInventoryFromQimen
    post /sync/inventory (SyncInventoryFromQimenReq) returns (SyncInventoryFromQimenResp)
    
    @doc "获取API同步日志"
    @handler GetQimenSyncLogs
    get /sync/logs (GetQimenSyncLogsReq) returns (GetQimenSyncLogsResp)
    
    @doc "重试失败的同步任务"
    @handler RetryQimenSync
    post /sync/retry (RetryQimenSyncReq) returns (RetryQimenSyncResp)
}