syntax = "v1"
import "base.api"

// 卡管理相关
type CardListReq {
   Page PageInfo `json:"page"`
   // ICCID
   ICCID *string `json:"iccid,optional"`
   // 卡片状态
   Status *int32 `json:"status,optional"`
   // 卡片标签
   Tags []string `json:"tags,optional"`
   // 锁定状态
   Locked *bool `json:"locked,optional"`
   // 所属运营商
   Channel *int32 `json:"channel,optional"`
   // 添加时间范围
   AddedDateRange []*int64 `json:"addedDateRange,optional"`
   // 创建时间范围
   CreatedAtRange []*int64 `json:"createdAtRange,optional"`
   // 关联设备
   DeviceNo *string `json:"deviceNo,optional"`
}

type CardChannelInfo {
    // 渠道ID
    ID int64 `json:"id"`
    // 渠道名称
    Name string `json:"name"`
    // 渠道运营商
    Operator string `json:"operator"`
    // real_name_required
    RealNameRequired bool `json:"realNameRequired"`
}

type CardListDeviceInfo {
    // 设备号
    DeviceNo string `json:"deviceNo"`
    // 设备ID
    ID int64 `json:"id"`
}

type CardInfo {
    // ID
    ID int64 `json:"id"`
    // ICCID
    ICCID string `json:"iccid"`
    // MSISDN
    MSISDN string `json:"msisdn"`
    // 卡片状态
    IMSI string `json:"imsi"`
    // 创建时间
    CreatedAt int64 `json:"createdAt"`
    // 导入时间
    AddedDate int64 `json:"addedDate"`
    // 所属标签
    Tags []string `json:"tags"`
    // 锁定状态
    Locked bool `json:"locked"`
    // 激活日期
    ActivatedAt int64 `json:"activatedAt"`
    // 卡片状态
    Status int32 `json:"status"`
    // 渠道相关
    Channel CardChannelInfo `json:"channel"`
    // 关联的设备信息
    Device *CardListDeviceInfo `json:"device,optional"`
}

type CardListInfo {
    // 卡列表
    BaseListInfo
    Data []CardInfo `json:"data"`
}

type CardListResp {
    BaseMsgResp
    Data CardListInfo `json:"data"`
}

type UploadCardFileResp {
    BaseMsgResp
}

type ImportCardReq {
    // 文件信息
    File FileInfo `json:"file"`
    // 卡片成本
    Cost float64 `json:"cost"`
    // 所属通道
    Channel int32 `json:"channel"`
    // 这批卡的Tag
    Tags []string `json:"tags,optional"`
}

type ChannelCreateReq {
    // 通道ID
    ID int64 `json:"id,optional"`
    // 通道名称
    Name string `json:"name"`
    // 所属地区
    Region string `json:"region,optional"`
    // 通道类型
    ChannelType string `json:"channelType"`
    // 所属运营商
    Operator string `json:"operator"`
    // 联系人
    ContactPerson string `json:"contactPerson,optional"`
    // 联系电话
    ContactPhone string `json:"contactPhone,optional"`
    // 是否需要实名认证
    RealNameRequired bool `json:"realNameRequired"`
    // 实名认证地址
    RealNameUrl string `json:"realNameUrl,optional"`
    // 通道余额
    Balance float64 `json:"balance,optional"`
    // 扩展数据
    ExtData string `json:"extData,optional"`
}

type ChannelCreateResp {
    BaseMsgResp
}

type ChannelListReq {
    Page PageInfo `json:"page"`
    // 通道名称
    Name *string `json:"name,optional"`
    // 所属地区
    Region *string `json:"region,optional"`
    // 通道类型
    ChannelType *string `json:"channelType,optional"`
    // 所属运营商
    Operator *string `json:"operator,optional"`
    // 实名认证
    RealNameRequired *bool `json:"realNameRequired,optional"`
}

type ChannelListItem {
    // ID
    ID int64 `json:"id"`
    // 通道名称
    Name string `json:"name"`
    // 所属地区
    Region string `json:"region"`
    // 通道类型
    ChannelType string `json:"channelType"`
    // 所属运营商
    Operator string `json:"operator"`
    // 联系人
    ContactPerson string `json:"contactPerson"`
    // 联系电话
    ContactPhone string `json:"contactPhone"`
    // 通道余额
    Balance float64 `json:"balance"`
    // 是否需要实名认证
    RealNameRequired bool `json:"realNameRequired"`
    // 实名认证地址
    RealNameUrl string `json:"realNameUrl"`
    // 扩展数据
    ExtData string `json:"extData"`
    // 创建时间
    CreatedAt int64 `json:"createdAt"`
}

type ChannelListInfo {
    BaseListInfo
    Data []ChannelListItem `json:"data"`
}

type ChannelListResp {
    BaseMsgResp
    Data ChannelListInfo `json:"data"`
}

type ImportRecordReq {
    Page PageInfo `json:"page"`
    // 通道ID
    ChannelID *int64 `json:"channelId,optional"`
}



@server(
	group: card
    prefix: /card
    middleware: Authority
    jwt: Auth
)

service Esim {
	// 获取卡列表
	@handler getCardList
	post /list(CardListReq) returns (CardListResp)

    // 卡片导入
    @handler importCard
    post /import(ImportCardReq) returns (BaseMsgResp)

    // 创建/更新通道
    @handler createChannel
    post /channel/create(ChannelCreateReq) returns (ChannelCreateResp)

    // 获取通道列表
    @handler getChannelList
    post /channel/list(ChannelListReq) returns (ChannelListResp)

}
