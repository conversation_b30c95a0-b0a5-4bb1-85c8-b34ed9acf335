syntax = "v1"
import "base.api"

type ImportDeviceReq {
    // 文件信息
    File FileInfo `json:"file"`
    // 所属通道
    Channel int32 `json:"channel"`
    // 设备成本
    Cost float64 `json:"cost"`
    // 这批设备的Tag
    Tags []string `json:"tags,optional"`    
    // 设备类型
    DeviceType string `json:"deviceType"`
    // 设备型号
    Model string `json:"model"`
}

type ImportDeviceResp {
    BaseMsgResp
}

type ManufacturerInfo {
    // ID
    ID int64 `json:"id"`
    // 厂家名称
    Name string `json:"name"`
    // 联系人姓名
    ContactPerson string `json:"contactPerson"`
    // 联系人电话
    ContactPhone string `json:"contactPhone"`
    // 联系人邮箱
    ContactEmail string `json:"contactEmail"`
    // 地址
    Address string `json:"address"`
    // 银行账户
    BankAccount string `json:"bankAccount"`
    // 开户行名称
    BankName string `json:"bankName"`
    // 创建时间
    CreatedAt int64 `json:"createdAt"`
    // 更新时间
    UpdatedAt int64 `json:"updatedAt"`
}

type ManufacturerCreateReq {
    // 厂家ID（修改时必填）
    ID *int64 `json:"id,optional"`
    // 厂家名称
    Name string `json:"name"`
    // 联系人姓名
    ContactPerson string `json:"contactPerson,optional"`
    // 联系人电话
    ContactPhone string `json:"contactPhone,optional"`
    // 联系人邮箱
    ContactEmail string `json:"contactEmail,optional"`
    // 地址
    Address string `json:"address,optional"`
    // 银行账户
    BankAccount string `json:"bankAccount,optional"`
    // 开户行名称
    BankName string `json:"bankName,optional"`
}

type ManufacturerCreateResp {
    BaseMsgResp
}

type ManufacturerListReq {
    Page PageInfo `json:"page"`
    // 厂家名称
    Name *string `json:"name,optional"`
    // 联系人
    ContactPerson *string `json:"contactPerson,optional"`
    // 联系电话
    ContactPhone *string `json:"contactPhone,optional"`
}

type ManufacturerListInfo {
    BaseListInfo
    Data []ManufacturerInfo `json:"data"`
}

type ManufacturerListResp {
    BaseMsgResp
    Data ManufacturerListInfo `json:"data"`
}

// 设备列表相关结构
type DeviceListReq {
    Page PageInfo `json:"page"`
    // 设备ID（设备号）
    DeviceNo *string `json:"deviceNo,optional"`
    // 设备IMEI
    Imei *string `json:"imei,optional"`
    // 卡的ICCID（通过关联卡片查询）
    Iccid *string `json:"iccid,optional"`
    // 设备标签
    Tags []string `json:"tags,optional"`
    // 所属渠道商ID
    ChannelId *int64 `json:"channelId,optional"`
    // 创建时间范围 - 开始时间（时间戳）
    CreatedAtStart *int64 `json:"createdAtStart,optional"`
    // 创建时间范围 - 结束时间（时间戳）
    CreatedAtEnd *int64 `json:"createdAtEnd,optional"`
    // 打包时间范围 - 开始时间（时间戳）
    PackageDateStart *int64 `json:"packageDateStart,optional"`
    // 打包时间范围 - 结束时间（时间戳）
    PackageDateEnd *int64 `json:"packageDateEnd,optional"`
}

// 渠道信息
type DeviceChannelInfo {
    // 渠道ID
    ID int64 `json:"id"`
    // 渠道名称
    Name string `json:"name"`
}

// 关联卡片信息
type DeviceCardInfo {
    // 卡片ID
    ID int64 `json:"id"`
    // ICCID
    Iccid string `json:"iccid"`
    // 手机号
    Msisdn string `json:"msisdn"`
    // IMSI
    Imsi string `json:"imsi"`
    // 卡片状态
    Status int32 `json:"status"`
    // 激活时间
    ActivatedAt int64 `json:"activatedAt"`
}

// 设备信息
type DeviceInfo {
    // 设备ID
    ID int64 `json:"id"`
    // 箱号
    BoxNo string `json:"boxNo"`
    // 随机码
    RandomCode string `json:"randomCode"`
    // 打包时间
    PackageDate int64 `json:"packageDate"`
    // 设备号
    DeviceNo string `json:"deviceNo"`
    // 设备IMEI号
    Imei string `json:"imei"`
    // 设备IMSI号
    Imsi string `json:"imsi"`
    // 设备MAC地址
    Mac string `json:"mac"`
    // 设备手机号
    Msisdn string `json:"msisdn"`
    // 设备的CCID
    Ccid string `json:"ccid"`
    // 接入号
    AccessNumber string `json:"accessNumber"`
    // 设备序列号
    Sn string `json:"sn"`
    // 设备无线网络名称
    Ssid string `json:"ssid"`
    // 设备无线网络密码
    WifiKey string `json:"wifiKey"`
    // 设备2.4G无线MAC地址
    WifiMac string `json:"wifiMac"`
    // 设备5G无线MAC地址
    WifiMac5g string `json:"wifiMac5g"`
    // 记录创建时间
    CreatedAt int64 `json:"createdAt"`
    // 记录更新时间
    UpdatedAt int64 `json:"updatedAt"`
    // 当前上行控速（0不限速，单位byte）
    SpeedUpLink int32 `json:"speedUpLink"`
    // 当前下行控速（0不限速，单位byte）
    SpeedDownLink int32 `json:"speedDownLink"`
    // WIFI状态（0关闭，1打开，2隐藏SSID）
    Hidden int32 `json:"hidden"`
    // 设备标签
    Tags []string `json:"tags"`
    // 当前设备成本
    CostPrice float64 `json:"costPrice"`
    // 渠道信息
    Channel *DeviceChannelInfo `json:"channel,optional"`
    // 关联的卡片信息
    Card []*DeviceCardInfo `json:"cards,optional"`
}

type DeviceListInfo {
    BaseListInfo
    Data []DeviceInfo `json:"data"`
}

type DeviceListResp {
    BaseMsgResp
    Data DeviceListInfo `json:"data"`
}

@server(
    group: device
    prefix: /device
    middleware: Authority
    jwt: Auth
)

service Esim {
    // 设备导入
    @handler importDevice
    post /import(ImportDeviceReq) returns (ImportDeviceResp)

    // 创建/修改设备厂商
    @handler createManufacturer
    post /manufacturer/create(ManufacturerCreateReq) returns (ManufacturerCreateResp)

    // 获取设备厂商列表
    @handler getManufacturerList
    post /manufacturer/list(ManufacturerListReq) returns (ManufacturerListResp)

    // 获取设备列表
    @handler getDeviceList
    post /list(DeviceListReq) returns (DeviceListResp)
}
